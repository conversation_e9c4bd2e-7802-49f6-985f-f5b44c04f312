const { DataSource } = require('typeorm');
const bcrypt = require('bcrypt');

const AppDataSource = new DataSource({
  type: 'postgres',
  host: 'localhost',
  port: 5432,
  username: 'syntext_user',
  password: 'syntext_password',
  database: 'syntext',
  synchronize: false,
  logging: false,
  entities: [],
});

const users = [
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '系统',
    lastName: '管理员',
    role: 'admin',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '项目',
    lastName: '经理',
    role: 'admin',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '演示',
    lastName: '账户',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '测试',
    lastName: '用户',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '张',
    lastName: '三',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '李',
    lastName: '四',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '王',
    lastName: '五',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '赵',
    lastName: '六',
    role: 'user',
    status: 'active'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: '陈',
    lastName: '七',
    role: 'user',
    status: 'active'
  }
];

async function createUsers() {
  try {
    await AppDataSource.initialize();
    console.log('✅ Database connected');

    for (const userData of users) {
      // Check if user already exists
      const existingUser = await AppDataSource.query(
        'SELECT id FROM users WHERE email = $1',
        [userData.email]
      );

      if (existingUser.length > 0) {
        console.log(`⏭️  User ${userData.email} already exists, skipping...`);
        continue;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Insert user
      await AppDataSource.query(`
        INSERT INTO users (id, email, password, "firstName", "lastName", role, status, "createdAt", "updatedAt")
        VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, NOW(), NOW())
      `, [
        userData.email,
        hashedPassword,
        userData.firstName,
        userData.lastName,
        userData.role,
        userData.status
      ]);

      console.log(`✅ Created user: ${userData.email} (${userData.firstName} ${userData.lastName})`);
    }

    console.log('\n🎉 All users created successfully!');

    // Show all users
    console.log('\n📋 Current users in database:');
    const allUsers = await AppDataSource.query('SELECT email, "firstName", "lastName", role FROM users ORDER BY email');
    allUsers.forEach(user => {
      console.log(`  - ${user.email} (${user.firstName} ${user.lastName}) - ${user.role}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await AppDataSource.destroy();
  }
}

createUsers();
