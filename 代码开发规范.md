# SynText 实时翻译系统 - 代码开发规范

## 📋 文档信息

| 项目名称 | SynText 实时翻译系统 |
|---------|-------------------|
| 版本 | 2.0 (重构版) |
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-03 |
| 更新日期 | 2025-08-03 |
| 负责人 | 开发团队 |

## 🎯 规范目标

本规范旨在确保代码质量、提高开发效率、降低维护成本，为团队协作提供统一的标准。

### 核心原则
- **一致性**: 保持代码风格和结构的一致性
- **可读性**: 代码应该易于理解和维护
- **可扩展性**: 支持功能扩展和模块化开发
- **安全性**: 遵循安全编码最佳实践
- **性能**: 考虑代码执行效率和资源使用

## 🏗️ 项目结构规范

### 整体项目结构
```
syntext/
├── backend/                    # 后端服务
│   ├── src/
│   │   ├── config/            # 配置管理
│   │   ├── core/              # 核心模块
│   │   ├── modules/           # 业务模块
│   │   ├── websocket/         # WebSocket服务
│   │   ├── database/          # 数据库相关
│   │   ├── utils/             # 工具函数
│   │   └── types/             # 类型定义
│   ├── test/                  # 测试文件
│   ├── dist/                  # 编译输出
│   └── package.json
├── frontend-admin/            # 管理后台
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── pages/            # 页面
│   │   ├── store/            # 状态管理
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── utils/            # 工具函数
│   │   ├── types/            # 类型定义
│   │   └── styles/           # 样式文件
│   └── package.json
├── frontend-client/           # 客户前端
│   └── (同管理后台结构)
├── shared/                    # 共享代码
│   ├── types/                # 共享类型
│   ├── utils/                # 共享工具
│   └── constants/            # 共享常量
├── docs/                     # 项目文档
├── scripts/                  # 构建脚本
└── docker-compose.yml        # Docker配置
```

### 模块组织原则
- **按功能分组**: 相关功能放在同一模块
- **单一职责**: 每个模块只负责一个功能领域
- **依赖管理**: 明确模块间的依赖关系
- **接口隔离**: 通过接口定义模块边界

## 💻 后端开发规范

### TypeScript编码规范

#### 1. 命名规范
```typescript
// 类名: PascalCase
class UserService {
  // 方法名: camelCase
  async createUser(userData: CreateUserDto): Promise<User> {
    // 变量名: camelCase
    const hashedPassword = await this.hashPassword(userData.password);
    
    // 常量: UPPER_SNAKE_CASE
    const DEFAULT_ROLE = 'user';
    
    return this.userRepository.save({
      ...userData,
      password: hashedPassword,
      role: DEFAULT_ROLE,
    });
  }
}

// 接口名: PascalCase, 以I开头
interface IUserRepository {
  findById(id: string): Promise<User | null>;
  create(userData: CreateUserDto): Promise<User>;
}

// 类型别名: PascalCase
type UserRole = 'user' | 'admin' | 'premium';

// 枚举: PascalCase
enum TranslationStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
}
```

#### 2. 文件命名规范
```
user.entity.ts          # 实体类
user.service.ts         # 服务类
user.controller.ts      # 控制器
user.module.ts          # 模块
user.dto.ts            # 数据传输对象
user.interface.ts      # 接口定义
user.spec.ts           # 测试文件
user.constants.ts      # 常量定义
```

#### 3. 代码组织规范
```typescript
// 文件头部注释
/**
 * 用户服务
 * 负责用户相关的业务逻辑处理
 * <AUTHOR>
 * @since 2.0.0
 */

// 导入顺序: 第三方库 -> 本地模块
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { User } from './user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UserRole } from '../types/user.types';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 创建新用户
   * @param userData 用户数据
   * @returns 创建的用户信息
   * @throws BadRequestException 当邮箱已存在时
   */
  async createUser(userData: CreateUserDto): Promise<User> {
    // 业务逻辑实现
  }
}
```

### NestJS架构规范

#### 1. 模块结构
```typescript
// user.module.ts
@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserProfile]),
    ConfigModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    UserRepository,
    {
      provide: 'USER_CONFIG',
      useFactory: (configService: ConfigService) => ({
        maxUsers: configService.get('user.maxUsers'),
      }),
      inject: [ConfigService],
    },
  ],
  exports: [UserService],
})
export class UserModule {}
```

#### 2. 控制器规范
```typescript
@ApiTags('users')
@Controller({ path: 'users', version: '1' })
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功', type: User })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async create(@Body() createUserDto: CreateUserDto): Promise<ApiResponse<User>> {
    const user = await this.userService.create(createUserDto);
    return {
      success: true,
      data: user,
      message: '用户创建成功',
    };
  }
}
```

#### 3. 服务层规范
```typescript
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      // 参数验证
      await this.validateUserData(createUserDto);
      
      // 业务逻辑
      const user = await this.userRepository.save({
        ...createUserDto,
        createdAt: new Date(),
      });

      this.logger.log(`User created: ${user.id}`);
      return user;
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`);
      throw error;
    }
  }

  private async validateUserData(userData: CreateUserDto): Promise<void> {
    // 验证逻辑
  }
}
```

### 数据库规范

#### 1. 实体定义
```typescript
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 255 })
  @Index()
  email: string;

  @Column({ name: 'password_hash', length: 255 })
  passwordHash: string;

  @Column({ name: 'first_name', length: 100, nullable: true })
  firstName?: string;

  @Column({ name: 'last_name', length: 100, nullable: true })
  lastName?: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => Translation, translation => translation.user)
  translations: Translation[];

  // 虚拟字段
  @Expose()
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // 方法
  async comparePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.passwordHash);
  }
}
```

#### 2. DTO规范
```typescript
export class CreateUserDto {
  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @ApiProperty({ description: '密码', minLength: 8, maxLength: 50 })
  @IsString({ message: '密码必须是字符串' })
  @MinLength(8, { message: '密码长度至少8位' })
  @MaxLength(50, { message: '密码长度不能超过50位' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  password: string;

  @ApiProperty({ description: '名字', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  firstName?: string;

  @ApiProperty({ description: '姓氏', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  lastName?: string;
}
```

## 🎨 前端开发规范

### React组件规范

#### 1. 组件命名和结构
```typescript
// UserProfile.tsx
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';

import { Button } from '../common/Button';
import { Input } from '../common/Input';
import { updateUserProfile } from '../../store/slices/userSlice';
import { RootState } from '../../store';
import { User } from '../../types/user.types';

interface UserProfileProps {
  userId: string;
  onUpdate?: (user: User) => void;
  className?: string;
}

/**
 * 用户资料组件
 * 用于显示和编辑用户基本信息
 */
export const UserProfile: React.FC<UserProfileProps> = ({
  userId,
  onUpdate,
  className = '',
}) => {
  // Hooks
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.user.currentUser);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });

  // Effects
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email,
      });
    }
  }, [user]);

  // Event handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const updatedUser = await dispatch(updateUserProfile(formData)).unwrap();
      onUpdate?.(updatedUser);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleInputChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  // Render
  return (
    <motion.div
      className={`bg-white rounded-lg shadow-md p-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">用户资料</h2>
        <Button
          variant="outline"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? '取消' : '编辑'}
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="名字"
          value={formData.firstName}
          onChange={handleInputChange('firstName')}
          disabled={!isEditing}
          placeholder="请输入名字"
        />
        
        <Input
          label="姓氏"
          value={formData.lastName}
          onChange={handleInputChange('lastName')}
          disabled={!isEditing}
          placeholder="请输入姓氏"
        />
        
        <Input
          label="邮箱"
          type="email"
          value={formData.email}
          onChange={handleInputChange('email')}
          disabled={!isEditing}
          placeholder="请输入邮箱"
        />

        {isEditing && (
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditing(false)}
            >
              取消
            </Button>
            <Button type="submit" variant="primary">
              保存
            </Button>
          </div>
        )}
      </form>
    </motion.div>
  );
};

export default UserProfile;
```

#### 2. 自定义Hook规范
```typescript
// useTranslation.ts
import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { translateText } from '../store/slices/translationSlice';
import { RootState } from '../store';
import { TranslateRequest, TranslationResult } from '../types/translation.types';

interface UseTranslationReturn {
  translate: (request: TranslateRequest) => Promise<TranslationResult>;
  isLoading: boolean;
  error: string | null;
  result: TranslationResult | null;
  clearResult: () => void;
}

/**
 * 翻译功能Hook
 * 提供翻译相关的状态和方法
 */
export const useTranslation = (): UseTranslationReturn => {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state: RootState) => state.translation);
  
  const [result, setResult] = useState<TranslationResult | null>(null);

  const translate = useCallback(async (request: TranslateRequest) => {
    try {
      const response = await dispatch(translateText(request)).unwrap();
      setResult(response);
      return response;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const clearResult = useCallback(() => {
    setResult(null);
  }, []);

  return {
    translate,
    isLoading,
    error,
    result,
    clearResult,
  };
};
```

### 状态管理规范

#### 1. Redux Slice规范
```typescript
// userSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { User, UpdateUserRequest } from '../../types/user.types';
import { userApi } from '../../api/user.api';

interface UserState {
  currentUser: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  currentUser: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await userApi.getProfile();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取用户信息失败');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (userData: UpdateUserRequest, { rejectWithValue }) => {
    try {
      const response = await userApi.updateProfile(userData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新用户信息失败');
    }
  }
);

// Slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
    },
    clearUser: (state) => {
      state.currentUser = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch user profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update user profile
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setUser, clearUser } = userSlice.actions;
export default userSlice.reducer;
```

### 样式规范

#### 1. Tailwind CSS使用规范
```typescript
// 组件样式示例
const styles = {
  // 基础样式
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  card: 'bg-white rounded-lg shadow-md p-6',
  
  // 按钮样式
  button: {
    base: 'inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200',
    primary: 'text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'text-primary-700 bg-primary-100 hover:bg-primary-200 focus:ring-primary-500',
    outline: 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-primary-500',
  },
  
  // 表单样式
  input: {
    base: 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
    error: 'border-red-300 focus:ring-red-500 focus:border-red-500',
  },
  
  // 文本样式
  text: {
    heading: 'text-2xl font-bold text-gray-900',
    subheading: 'text-lg font-semibold text-gray-800',
    body: 'text-base text-gray-700',
    caption: 'text-sm text-gray-500',
    error: 'text-sm text-red-600',
  },
};

// 使用示例
export const LoginForm: React.FC = () => {
  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1 className={styles.text.heading}>用户登录</h1>
        <form className="mt-6 space-y-4">
          <input
            type="email"
            className={styles.input.base}
            placeholder="邮箱地址"
          />
          <button
            type="submit"
            className={`${styles.button.base} ${styles.button.primary} w-full`}
          >
            登录
          </button>
        </form>
      </div>
    </div>
  );
};
```

#### 2. 设计系统颜色规范
```typescript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        // 主色调 - 蓝色系
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',  // 主色
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        
        // 辅助色 - 灰色系
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        
        // 状态色
        success: {
          50: '#ecfdf5',
          500: '#10b981',
          600: '#059669',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        error: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
      },
      
      // 字体
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      
      // 间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      
      // 阴影
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      },
    },
  },
};
```

## 🧪 测试规范

### 单元测试规范
```typescript
// user.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UserService } from './user.service';
import { User } from './user.entity';
import { CreateUserDto } from './dto/create-user.dto';

describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  const mockRepository = {
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a user successfully', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const expectedUser = {
        id: 'uuid-string',
        ...createUserDto,
        createdAt: new Date(),
      };

      mockRepository.save.mockResolvedValue(expectedUser);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(result).toEqual(expectedUser);
      expect(mockRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          email: createUserDto.email,
          firstName: createUserDto.firstName,
          lastName: createUserDto.lastName,
        })
      );
    });

    it('should throw error when email already exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      mockRepository.save.mockRejectedValue(
        new Error('Duplicate entry')
      );

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow();
    });
  });
});
```

### 集成测试规范
```typescript
// user.controller.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';

import { AppModule } from '../src/app.module';

describe('UserController (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // 获取认证token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.data.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/users (POST)', () => {
    it('should create a new user', () => {
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'New',
          lastName: 'User',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.email).toBe('<EMAIL>');
        });
    });
  });
});
```

## 📝 Git规范

### 提交信息规范
```bash
# 格式: <type>(<scope>): <subject>
#
# type: 提交类型
# - feat: 新功能
# - fix: 修复bug
# - docs: 文档更新
# - style: 代码格式调整
# - refactor: 重构
# - test: 测试相关
# - chore: 构建过程或辅助工具的变动

# 示例
feat(auth): add JWT token refresh mechanism
fix(translation): resolve memory leak in batch processing
docs(api): update authentication endpoints documentation
style(frontend): format code with prettier
refactor(user): extract user validation logic
test(translation): add unit tests for translation service
chore(deps): update dependencies to latest versions
```

### 分支管理规范
```bash
# 主分支
main                    # 生产环境分支
develop                 # 开发环境分支

# 功能分支
feature/user-auth       # 用户认证功能
feature/translation-ui  # 翻译界面功能

# 修复分支
hotfix/security-patch   # 安全补丁
bugfix/login-error     # 登录错误修复

# 发布分支
release/v2.0.0         # 版本发布分支
```

## 🔧 工具配置

### ESLint配置
```json
{
  "extends": [
    "@nestjs/eslint-config",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Prettier配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "strictPropertyInitialization": false,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"]
    }
  }
}
```

## 📊 性能规范

### 前端性能优化
- 使用React.memo优化组件渲染
- 实现虚拟滚动处理大列表
- 使用懒加载减少初始包大小
- 优化图片资源和静态资源
- 实现适当的缓存策略

### 后端性能优化
- 数据库查询优化和索引使用
- 实现适当的缓存机制
- 使用连接池管理数据库连接
- 异步处理耗时操作
- 实现请求限流和防抖

## 🔒 安全规范

### 输入验证
- 所有用户输入必须验证
- 使用DTO进行数据传输对象验证
- 实现SQL注入防护
- 防止XSS攻击

### 认证授权
- 使用强密码策略
- 实现JWT token机制
- 添加请求频率限制
- 记录安全相关日志

## 📚 文档规范

### 代码注释
- 类和方法必须有JSDoc注释
- 复杂业务逻辑添加行内注释
- 接口和类型定义添加描述
- 保持注释与代码同步更新

### API文档
- 使用Swagger自动生成API文档
- 提供请求/响应示例
- 说明错误码和处理方式
- 保持文档版本与代码同步

这个代码开发规范为团队提供了统一的编码标准，确保代码质量和项目的可维护性。
