# SynText 实时翻译系统 - 系统设计文档

## 📋 文档信息

| 项目名称 | SynText 实时翻译系统 |
|---------|-------------------|
| 版本 | 2.0 (重构版) |
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-03 |
| 更新日期 | 2025-08-03 |
| 负责人 | 架构团队 |

## 🏗️ 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台       │    │   客户前端       │    │  实时翻译页面    │
│  (Admin Panel)  │    │ (Client App)    │    │ (Realtime Page) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   负载均衡器     │
                    │ (Load Balancer) │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │  (API Gateway)  │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   认证服务       │    │   翻译服务       │    │   管理服务       │
│ (Auth Service)  │    │(Translation Svc)│    │ (Admin Service) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据访问层     │
                    │ (Data Access)   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   AI服务商      │
│   (主数据库)     │    │    (缓存)       │    │ (DeepSeek等)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈选择

#### 后端技术栈
- **框架**: NestJS (基于Node.js)
- **语言**: TypeScript
- **ORM**: TypeORM
- **认证**: JWT + Passport
- **实时通信**: Socket.io
- **API文档**: Swagger/OpenAPI
- **日志**: Winston
- **测试**: Jest

#### 前端技术栈
- **框架**: React 18
- **语言**: TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **状态管理**: Redux Toolkit
- **路由**: React Router
- **HTTP客户端**: Axios
- **动画**: Framer Motion

#### 数据库技术栈
- **主数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **连接池**: TypeORM连接池
- **迁移**: TypeORM Migrations

#### 部署技术栈
- **容器化**: Docker + Docker Compose
- **服务器**: 云服务器 (**************)
- **域名**: syntext.cn
- **反向代理**: Nginx
- **进程管理**: PM2

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    username VARCHAR(100),
    role user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    profile JSONB DEFAULT '{}'::jsonb
);
```

#### 翻译记录表 (translations)
```sql
CREATE TABLE translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    original_text TEXT NOT NULL,
    translated_text TEXT,
    source_language VARCHAR(10) NOT NULL,
    target_language VARCHAR(10) NOT NULL,
    ai_provider ai_provider DEFAULT 'deepseek',
    status translation_status DEFAULT 'pending',
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb
);
```

#### 订阅表 (subscriptions)
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_type subscription_plan DEFAULT 'free',
    status subscription_status DEFAULT 'active',
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    usage_limit INTEGER DEFAULT 1000,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 优惠码表 (coupons)
```sql
CREATE TABLE coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_type coupon_type DEFAULT 'percentage',
    discount_value DECIMAL(10,2) NOT NULL,
    max_uses INTEGER,
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 数据关系设计
- 用户与翻译记录: 1对多关系
- 用户与订阅: 1对1关系
- 用户与支付记录: 1对多关系
- 优惠码与使用记录: 1对多关系

### 索引策略
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 翻译记录索引
CREATE INDEX idx_translations_user_id ON translations(user_id);
CREATE INDEX idx_translations_created_at ON translations(created_at);
CREATE INDEX idx_translations_language_pair ON translations(source_language, target_language);

-- 订阅表索引
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
```

## 🔧 模块设计

### 后端模块架构

#### 核心模块 (core/)
```
core/
├── auth/                 # 认证授权模块
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   ├── auth.module.ts
│   ├── strategies/       # 认证策略
│   └── guards/          # 路由守卫
├── database/            # 数据库连接
├── redis/              # Redis连接
└── logger/             # 日志系统
```

#### 业务模块 (modules/)
```
modules/
├── user/               # 用户管理
├── translation/        # 翻译服务
├── ai/                # AI服务集成
├── subscription/       # 订阅管理
├── payment/           # 支付处理
├── admin/             # 管理功能
├── analytics/         # 数据分析
└── health/            # 健康检查
```

#### WebSocket模块 (websocket/)
```
websocket/
├── websocket.gateway.ts    # WebSocket网关
├── websocket.module.ts     # WebSocket模块
└── events/                # 事件处理
```

### 前端模块架构

#### 管理后台结构
```
frontend-admin/
├── src/
│   ├── components/        # 通用组件
│   │   ├── common/       # 基础组件
│   │   ├── forms/        # 表单组件
│   │   ├── charts/       # 图表组件
│   │   └── layout/       # 布局组件
│   ├── pages/            # 页面组件
│   │   ├── DashboardPage.tsx
│   │   ├── UsersPage.tsx
│   │   ├── TranslationsPage.tsx
│   │   └── SettingsPage.tsx
│   ├── store/            # 状态管理
│   │   ├── slices/       # Redux切片
│   │   └── index.ts      # Store配置
│   ├── hooks/            # 自定义Hooks
│   ├── utils/            # 工具函数
│   └── types/            # 类型定义
```

#### 客户前端结构
```
frontend-client/
├── src/
│   ├── components/        # 通用组件
│   ├── pages/            # 页面组件
│   │   ├── HomePage.tsx
│   │   ├── TranslatePage.tsx
│   │   ├── HistoryPage.tsx
│   │   └── ProfilePage.tsx
│   ├── store/            # 状态管理
│   ├── hooks/            # 自定义Hooks
│   └── utils/            # 工具函数
```

## 🔌 接口设计

### RESTful API设计原则
- 使用HTTP动词表示操作 (GET, POST, PUT, DELETE)
- 使用名词表示资源
- 统一的响应格式
- 版本控制 (/api/v1/)
- 错误处理标准化

### API响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}
```

### 核心API端点

#### 认证相关
```
POST /api/v1/auth/register     # 用户注册
POST /api/v1/auth/login        # 用户登录
POST /api/v1/auth/refresh      # 刷新Token
POST /api/v1/auth/logout       # 用户登出
POST /api/v1/auth/forgot       # 忘记密码
```

#### 翻译相关
```
POST /api/v1/translation/translate    # 文本翻译
GET  /api/v1/translation/history      # 翻译历史
GET  /api/v1/translation/languages    # 支持语言
POST /api/v1/translation/batch        # 批量翻译
```

#### 用户相关
```
GET  /api/v1/user/profile      # 获取用户信息
PUT  /api/v1/user/profile      # 更新用户信息
GET  /api/v1/user/subscription # 获取订阅信息
GET  /api/v1/user/usage        # 获取使用统计
```

### WebSocket事件设计
```typescript
// 客户端发送事件
interface ClientEvents {
  authenticate: (data: { token: string }) => void;
  translate: (data: TranslateRequest) => void;
  join_room: (data: { roomId: string }) => void;
}

// 服务端发送事件
interface ServerEvents {
  authenticated: (data: { userId: string }) => void;
  translation_result: (data: TranslationResult) => void;
  translation_progress: (data: { progress: number }) => void;
  error: (data: { message: string }) => void;
}
```

## 🔒 安全设计

### 认证安全
- JWT Token + Refresh Token机制
- Token过期时间: Access Token 7天, Refresh Token 30天
- 密码加密: bcrypt (10轮)
- 登录失败限制: 5次失败锁定账户

### API安全
- HTTPS强制加密
- CORS跨域控制
- 请求频率限制 (Rate Limiting)
- 输入验证和清理
- SQL注入防护

### 数据安全
- 敏感数据加密存储
- 数据库连接加密
- 定期数据备份
- 访问日志记录

## 🚀 性能设计

### 缓存策略
- Redis缓存翻译结果 (TTL: 1小时)
- 用户会话缓存 (TTL: 30分钟)
- API响应缓存 (TTL: 5分钟)
- 静态资源CDN缓存

### 数据库优化
- 连接池配置 (最大10个连接)
- 查询优化和索引
- 读写分离 (如需要)
- 分页查询限制

### 前端性能
- 代码分割和懒加载
- 图片压缩和优化
- 静态资源压缩
- 浏览器缓存策略

## 📊 监控设计

### 系统监控
- 服务器资源监控 (CPU, 内存, 磁盘)
- 数据库性能监控
- API响应时间监控
- 错误率监控

### 业务监控
- 用户活跃度统计
- 翻译使用量统计
- 支付成功率监控
- 功能使用情况分析

### 日志设计
```typescript
interface LogEntry {
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  service: string;
  message: string;
  metadata?: {
    userId?: string;
    requestId?: string;
    ip?: string;
    userAgent?: string;
  };
}
```

## 🔄 部署设计

### 环境配置
- **开发环境**: 本地Docker开发
- **测试环境**: 云服务器测试部署
- **生产环境**: 云服务器生产部署

### Docker配置
```dockerfile
# 后端Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/main.js"]
```

### 部署流程
1. 代码构建和测试
2. Docker镜像构建
3. 数据库迁移
4. 服务部署
5. 健康检查
6. 流量切换

## 📈 扩展设计

### 水平扩展
- 无状态服务设计
- 负载均衡配置
- 数据库读写分离
- 缓存集群

### 功能扩展
- 插件化架构
- API版本管理
- 第三方集成接口
- 微服务拆分准备

## 🧪 测试设计

### 测试策略
- **单元测试**: Jest + 覆盖率 > 80%
- **集成测试**: API接口测试
- **端到端测试**: Cypress自动化测试
- **性能测试**: 负载测试和压力测试

### 测试环境
- 独立的测试数据库
- Mock外部服务
- 自动化测试流水线
- 测试数据管理

## 📝 开发规范

### 代码规范
- TypeScript严格模式
- ESLint + Prettier代码格式化
- Git提交规范 (Conventional Commits)
- 代码审查流程

### 文档规范
- API文档自动生成 (Swagger)
- 代码注释规范
- 变更日志维护
- 部署文档更新

这个设计文档为重构项目提供了完整的技术架构指导，确保开发团队能够按照统一的标准进行开发。
