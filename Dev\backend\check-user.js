const { DataSource } = require('typeorm');
const bcrypt = require('bcrypt');

const AppDataSource = new DataSource({
  type: 'postgres',
  host: 'localhost',
  port: 5432,
  username: 'syntext_user',
  password: 'syntext_password',
  database: 'syntext',
  synchronize: false,
  logging: false,
  entities: [],
});

async function checkUser() {
  try {
    await AppDataSource.initialize();
    console.log('✅ Database connected');

    // Check if user exists
    const userQuery = `
      SELECT id, email, "firstName", "lastName", password, status, role, "createdAt"
      FROM users 
      WHERE email = $1
    `;
    
    const result = await AppDataSource.query(userQuery, ['<EMAIL>']);
    
    if (result.length === 0) {
      console.log('❌ User <EMAIL> not found in database');
      
      // Let's see what users exist
      console.log('\n📋 Existing users:');
      const allUsers = await AppDataSource.query('SELECT email, "firstName", "lastName", role FROM users ORDER BY email');
      allUsers.forEach(user => {
        console.log(`  - ${user.email} (${user.firstName} ${user.lastName}) - ${user.role}`);
      });
      
    } else {
      const user = result[0];
      console.log('✅ User found:');
      console.log(`  - ID: ${user.id}`);
      console.log(`  - Email: ${user.email}`);
      console.log(`  - Name: ${user.firstName} ${user.lastName}`);
      console.log(`  - Role: ${user.role}`);
      console.log(`  - Status: ${user.status}`);
      console.log(`  - Created: ${user.createdAt}`);
      
      // Test password
      const testPassword = '123456';
      const isValidPassword = await bcrypt.compare(testPassword, user.password);
      console.log(`  - Password test (123456): ${isValidPassword ? '✅ Valid' : '❌ Invalid'}`);
      
      if (!isValidPassword) {
        console.log(`  - Stored hash: ${user.password}`);
        console.log(`  - Expected hash for '123456': ${await bcrypt.hash('123456', 10)}`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await AppDataSource.destroy();
  }
}

checkUser();
