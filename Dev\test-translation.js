const { default: fetch } = require('node-fetch');

const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.gdyarxNMFIK7Fg_23Ue5VeElJu1ohmH38TMLDdByUsI';

async function testTranslation(text, sourceLang, targetLang, description) {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📝 Input: "${text}" (${sourceLang} -> ${targetLang})`);
  
  try {
    const response = await fetch('http://localhost:4000/api/v1/translation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Authorization': `Bearer ${JWT_TOKEN}`
      },
      body: JSON.stringify({
        text: text,
        sourceLang: sourceLang,
        targetLang: targetLang
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ Success: "${result.data.translatedText}"`);
      console.log(`⏱️  Latency: ${result.data.latency}ms`);
      console.log(`🎯 Confidence: ${result.data.confidence}`);
    } else {
      console.log(`❌ Failed: ${result.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting Translation Service Tests...\n');

  // Test 1: English to Chinese
  await testTranslation(
    'Hello, how are you today?',
    'en',
    'zh',
    'English to Chinese'
  );

  // Test 2: Chinese to English
  await testTranslation(
    '人工智能正在改变我们的生活方式',
    'zh',
    'en',
    'Chinese to English'
  );

  // Test 3: Long text translation
  await testTranslation(
    'Artificial intelligence is revolutionizing the way we work, communicate, and solve complex problems in various industries.',
    'en',
    'zh',
    'Long English text to Chinese'
  );

  // Test 4: Technical terms
  await testTranslation(
    'Machine learning algorithms can process large datasets efficiently.',
    'en',
    'zh',
    'Technical terms translation'
  );

  console.log('\n🏁 All tests completed!');
}

runTests().catch(console.error);
