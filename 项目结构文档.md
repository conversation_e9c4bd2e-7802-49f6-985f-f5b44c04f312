# SynText 实时翻译系统 - 项目结构文档

## 📋 文档信息

| 项目名称 | SynText 实时翻译系统 |
|---------|-------------------|
| 版本 | 2.0 (重构版) |
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-03 |
| 更新日期 | 2025-08-03 |
| 负责人 | 架构团队 |

## 🏗️ 整体项目结构

### 根目录结构
```
syntext/
├── README.md                   # 项目说明文档
├── LICENSE                     # 开源协议
├── .gitignore                  # Git忽略文件
├── .env.example               # 环境变量模板
├── docker-compose.yml         # Docker编排配置
├── docker-compose.prod.yml    # 生产环境Docker配置
├── package.json               # 根项目配置
├── lerna.json                 # Monorepo配置
├── tsconfig.json              # TypeScript根配置
├── .eslintrc.js              # ESLint配置
├── .prettierrc               # Prettier配置
├── backend/                   # 后端服务
├── frontend-admin/            # 管理后台
├── frontend-client/           # 客户前端
├── shared/                    # 共享代码库
├── docs/                      # 项目文档
├── scripts/                   # 构建和部署脚本
├── tests/                     # 端到端测试
└── deployment/                # 部署配置
```

### 项目架构说明
- **Monorepo架构**: 使用Lerna管理多个子项目
- **微服务设计**: 后端采用模块化设计，便于扩展
- **前后端分离**: 管理后台和客户前端独立部署
- **共享代码库**: 类型定义和工具函数复用
- **容器化部署**: 使用Docker进行统一部署

## 🔧 后端服务结构

### 目录结构详解
```
backend/
├── src/                       # 源代码目录
│   ├── main.ts               # 应用入口文件
│   ├── app.module.ts         # 根模块
│   ├── app.controller.ts     # 根控制器
│   ├── app.service.ts        # 根服务
│   ├── config/               # 配置管理
│   │   ├── index.ts          # 配置导出
│   │   ├── database.config.ts # 数据库配置
│   │   ├── redis.config.ts   # Redis配置
│   │   ├── jwt.config.ts     # JWT配置
│   │   ├── ai.config.ts      # AI服务配置
│   │   └── app.config.ts     # 应用配置
│   ├── core/                 # 核心模块
│   │   ├── auth/             # 认证授权
│   │   │   ├── auth.module.ts
│   │   │   ├── auth.controller.ts
│   │   │   ├── auth.service.ts
│   │   │   ├── strategies/   # 认证策略
│   │   │   │   ├── jwt.strategy.ts
│   │   │   │   └── local.strategy.ts
│   │   │   ├── guards/       # 路由守卫
│   │   │   │   ├── jwt-auth.guard.ts
│   │   │   │   ├── roles.guard.ts
│   │   │   │   └── throttler.guard.ts
│   │   │   └── decorators/   # 装饰器
│   │   │       ├── roles.decorator.ts
│   │   │       └── public.decorator.ts
│   │   ├── database/         # 数据库连接
│   │   │   ├── database.module.ts
│   │   │   ├── database.providers.ts
│   │   │   └── migrations/   # 数据库迁移
│   │   ├── redis/            # Redis连接
│   │   │   ├── redis.module.ts
│   │   │   └── redis.service.ts
│   │   ├── logger/           # 日志系统
│   │   │   ├── logger.module.ts
│   │   │   ├── logger.service.ts
│   │   │   └── winston.config.ts
│   │   └── interceptors/     # 拦截器
│   │       ├── response.interceptor.ts
│   │       ├── logging.interceptor.ts
│   │       └── timeout.interceptor.ts
│   ├── modules/              # 业务模块
│   │   ├── user/             # 用户管理
│   │   │   ├── user.module.ts
│   │   │   ├── user.controller.ts
│   │   │   ├── user.service.ts
│   │   │   ├── user.entity.ts
│   │   │   ├── user.repository.ts
│   │   │   ├── dto/          # 数据传输对象
│   │   │   │   ├── create-user.dto.ts
│   │   │   │   ├── update-user.dto.ts
│   │   │   │   └── user-response.dto.ts
│   │   │   └── interfaces/   # 接口定义
│   │   │       └── user.interface.ts
│   │   ├── translation/      # 翻译服务
│   │   │   ├── translation.module.ts
│   │   │   ├── translation.controller.ts
│   │   │   ├── translation.service.ts
│   │   │   ├── translation.entity.ts
│   │   │   ├── translation.repository.ts
│   │   │   ├── dto/
│   │   │   │   ├── translate-request.dto.ts
│   │   │   │   ├── batch-translate.dto.ts
│   │   │   │   └── translation-response.dto.ts
│   │   │   └── processors/   # 翻译处理器
│   │   │       ├── deepseek.processor.ts
│   │   │       ├── volcengine.processor.ts
│   │   │       └── base.processor.ts
│   │   ├── ai/               # AI服务集成
│   │   │   ├── ai.module.ts
│   │   │   ├── ai.service.ts
│   │   │   ├── providers/    # AI提供商
│   │   │   │   ├── deepseek.provider.ts
│   │   │   │   ├── volcengine.provider.ts
│   │   │   │   └── base.provider.ts
│   │   │   └── interfaces/
│   │   │       └── ai-provider.interface.ts
│   │   ├── subscription/     # 订阅管理
│   │   │   ├── subscription.module.ts
│   │   │   ├── subscription.controller.ts
│   │   │   ├── subscription.service.ts
│   │   │   ├── subscription.entity.ts
│   │   │   ├── plan.entity.ts
│   │   │   └── dto/
│   │   │       ├── create-subscription.dto.ts
│   │   │       └── update-subscription.dto.ts
│   │   ├── payment/          # 支付处理
│   │   │   ├── payment.module.ts
│   │   │   ├── payment.controller.ts
│   │   │   ├── payment.service.ts
│   │   │   ├── payment.entity.ts
│   │   │   ├── coupon.entity.ts
│   │   │   ├── processors/   # 支付处理器
│   │   │   │   ├── alipay.processor.ts
│   │   │   │   ├── wechat.processor.ts
│   │   │   │   └── stripe.processor.ts
│   │   │   └── dto/
│   │   │       ├── payment-request.dto.ts
│   │   │       └── coupon.dto.ts
│   │   ├── admin/            # 管理功能
│   │   │   ├── admin.module.ts
│   │   │   ├── admin.controller.ts
│   │   │   ├── admin.service.ts
│   │   │   └── dto/
│   │   │       ├── admin-stats.dto.ts
│   │   │       └── user-management.dto.ts
│   │   ├── analytics/        # 数据分析
│   │   │   ├── analytics.module.ts
│   │   │   ├── analytics.controller.ts
│   │   │   ├── analytics.service.ts
│   │   │   ├── analytics.entity.ts
│   │   │   └── dto/
│   │   │       └── analytics-query.dto.ts
│   │   └── health/           # 健康检查
│   │       ├── health.module.ts
│   │       ├── health.controller.ts
│   │       └── health.service.ts
│   ├── websocket/            # WebSocket服务
│   │   ├── websocket.module.ts
│   │   ├── websocket.gateway.ts
│   │   ├── websocket.service.ts
│   │   ├── events/           # 事件处理
│   │   │   ├── translation.events.ts
│   │   │   ├── user.events.ts
│   │   │   └── system.events.ts
│   │   └── interfaces/
│   │       ├── websocket-client.interface.ts
│   │       └── websocket-events.interface.ts
│   ├── database/             # 数据库相关
│   │   ├── entities/         # 实体定义
│   │   │   ├── index.ts      # 实体导出
│   │   │   ├── user.entity.ts
│   │   │   ├── translation.entity.ts
│   │   │   ├── subscription.entity.ts
│   │   │   ├── payment.entity.ts
│   │   │   └── analytics.entity.ts
│   │   ├── migrations/       # 数据库迁移
│   │   │   ├── 1691000000000-CreateUserTable.ts
│   │   │   ├── 1691000001000-CreateTranslationTable.ts
│   │   │   ├── 1691000002000-CreateSubscriptionTable.ts
│   │   │   └── 1691000003000-CreatePaymentTable.ts
│   │   ├── seeds/            # 数据种子
│   │   │   ├── user.seed.ts
│   │   │   ├── plan.seed.ts
│   │   │   └── admin.seed.ts
│   │   └── repositories/     # 自定义仓库
│   │       ├── user.repository.ts
│   │       ├── translation.repository.ts
│   │       └── analytics.repository.ts
│   ├── utils/                # 工具函数
│   │   ├── crypto.util.ts    # 加密工具
│   │   ├── date.util.ts      # 日期工具
│   │   ├── validation.util.ts # 验证工具
│   │   ├── response.util.ts  # 响应工具
│   │   └── pagination.util.ts # 分页工具
│   ├── types/                # 类型定义
│   │   ├── common.types.ts   # 通用类型
│   │   ├── auth.types.ts     # 认证类型
│   │   ├── translation.types.ts # 翻译类型
│   │   ├── payment.types.ts  # 支付类型
│   │   └── websocket.types.ts # WebSocket类型
│   ├── constants/            # 常量定义
│   │   ├── app.constants.ts  # 应用常量
│   │   ├── error.constants.ts # 错误常量
│   │   ├── role.constants.ts # 角色常量
│   │   └── translation.constants.ts # 翻译常量
│   └── filters/              # 异常过滤器
│       ├── http-exception.filter.ts
│       ├── validation.filter.ts
│       └── all-exceptions.filter.ts
├── test/                     # 测试文件
│   ├── unit/                 # 单元测试
│   │   ├── user/
│   │   ├── translation/
│   │   └── auth/
│   ├── integration/          # 集成测试
│   │   ├── user.e2e-spec.ts
│   │   ├── translation.e2e-spec.ts
│   │   └── auth.e2e-spec.ts
│   ├── fixtures/             # 测试数据
│   │   ├── user.fixture.ts
│   │   └── translation.fixture.ts
│   └── utils/                # 测试工具
│       ├── test-db.util.ts
│       └── mock.util.ts
├── dist/                     # 编译输出
├── node_modules/             # 依赖包
├── package.json              # 项目配置
├── package-lock.json         # 依赖锁定
├── tsconfig.json             # TypeScript配置
├── tsconfig.build.json       # 构建配置
├── nest-cli.json             # NestJS CLI配置
├── .env                      # 环境变量
├── .env.example              # 环境变量模板
├── Dockerfile                # Docker镜像
├── .dockerignore             # Docker忽略文件
├── .gitignore                # Git忽略文件
├── README.md                 # 项目说明
└── CHANGELOG.md              # 变更日志
```

### 模块职责说明

#### 核心模块 (core/)
- **auth/**: 处理用户认证、授权、JWT令牌管理
- **database/**: 数据库连接配置和管理
- **redis/**: Redis缓存服务配置
- **logger/**: 统一日志记录和管理
- **interceptors/**: 请求/响应拦截器

#### 业务模块 (modules/)
- **user/**: 用户管理，包括注册、登录、资料管理
- **translation/**: 翻译服务核心功能
- **ai/**: AI服务提供商集成和管理
- **subscription/**: 订阅套餐和用户订阅管理
- **payment/**: 支付处理和优惠码管理
- **admin/**: 管理后台功能
- **analytics/**: 数据统计和分析
- **health/**: 系统健康检查

#### WebSocket模块 (websocket/)
- **gateway**: WebSocket连接管理
- **events**: 实时事件处理
- **interfaces**: WebSocket相关接口定义

## 🎨 前端项目结构

### 管理后台结构
```
frontend-admin/
├── public/                   # 静态资源
│   ├── index.html           # HTML模板
│   ├── favicon.ico          # 网站图标
│   ├── manifest.json        # PWA配置
│   └── robots.txt           # 爬虫配置
├── src/                     # 源代码
│   ├── main.tsx             # 应用入口
│   ├── App.tsx              # 根组件
│   ├── App.css              # 全局样式
│   ├── vite-env.d.ts        # Vite类型声明
│   ├── components/          # 组件库
│   │   ├── common/          # 通用组件
│   │   │   ├── Button/      # 按钮组件
│   │   │   │   ├── index.tsx
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Button.module.css
│   │   │   │   └── Button.stories.tsx
│   │   │   ├── Input/       # 输入框组件
│   │   │   ├── Modal/       # 模态框组件
│   │   │   ├── Table/       # 表格组件
│   │   │   ├── Pagination/  # 分页组件
│   │   │   ├── Loading/     # 加载组件
│   │   │   └── ErrorBoundary/ # 错误边界
│   │   ├── forms/           # 表单组件
│   │   │   ├── UserForm/    # 用户表单
│   │   │   ├── CouponForm/  # 优惠码表单
│   │   │   └── SettingsForm/ # 设置表单
│   │   ├── charts/          # 图表组件
│   │   │   ├── LineChart/   # 折线图
│   │   │   ├── BarChart/    # 柱状图
│   │   │   ├── PieChart/    # 饼图
│   │   │   └── StatCard/    # 统计卡片
│   │   └── layout/          # 布局组件
│   │       ├── Header/      # 头部导航
│   │       ├── Sidebar/     # 侧边栏
│   │       ├── Footer/      # 页脚
│   │       └── Layout/      # 主布局
│   ├── pages/               # 页面组件
│   │   ├── DashboardPage/   # 仪表盘页面
│   │   │   ├── index.tsx
│   │   │   ├── DashboardPage.tsx
│   │   │   ├── components/  # 页面专用组件
│   │   │   │   ├── StatsOverview/
│   │   │   │   ├── RecentActivity/
│   │   │   │   └── QuickActions/
│   │   │   └── hooks/       # 页面专用Hooks
│   │   │       └── useDashboard.ts
│   │   ├── UsersPage/       # 用户管理页面
│   │   │   ├── index.tsx
│   │   │   ├── UsersPage.tsx
│   │   │   ├── components/
│   │   │   │   ├── UserList/
│   │   │   │   ├── UserDetail/
│   │   │   │   └── UserActions/
│   │   │   └── hooks/
│   │   │       └── useUsers.ts
│   │   ├── TranslationsPage/ # 翻译管理页面
│   │   ├── AnalyticsPage/   # 数据分析页面
│   │   ├── SettingsPage/    # 系统设置页面
│   │   ├── LoginPage/       # 登录页面
│   │   └── NotFoundPage/    # 404页面
│   ├── store/               # 状态管理
│   │   ├── index.ts         # Store配置
│   │   ├── rootReducer.ts   # 根Reducer
│   │   ├── middleware.ts    # 中间件配置
│   │   └── slices/          # Redux切片
│   │       ├── authSlice.ts # 认证状态
│   │       ├── userSlice.ts # 用户状态
│   │       ├── translationSlice.ts # 翻译状态
│   │       ├── analyticsSlice.ts # 分析状态
│   │       └── uiSlice.ts   # UI状态
│   ├── hooks/               # 自定义Hooks
│   │   ├── useAuth.ts       # 认证Hook
│   │   ├── useApi.ts        # API调用Hook
│   │   ├── useLocalStorage.ts # 本地存储Hook
│   │   ├── useDebounce.ts   # 防抖Hook
│   │   └── usePermissions.ts # 权限Hook
│   ├── services/            # 服务层
│   │   ├── api/             # API服务
│   │   │   ├── index.ts     # API配置
│   │   │   ├── auth.api.ts  # 认证API
│   │   │   ├── user.api.ts  # 用户API
│   │   │   ├── translation.api.ts # 翻译API
│   │   │   ├── analytics.api.ts # 分析API
│   │   │   └── admin.api.ts # 管理API
│   │   ├── websocket/       # WebSocket服务
│   │   │   ├── index.ts
│   │   │   ├── websocket.service.ts
│   │   │   └── events.ts
│   │   └── storage/         # 存储服务
│   │       ├── localStorage.service.ts
│   │       └── sessionStorage.service.ts
│   ├── utils/               # 工具函数
│   │   ├── format.ts        # 格式化工具
│   │   ├── validation.ts    # 验证工具
│   │   ├── date.ts          # 日期工具
│   │   ├── export.ts        # 导出工具
│   │   └── constants.ts     # 常量定义
│   ├── types/               # 类型定义
│   │   ├── api.types.ts     # API类型
│   │   ├── user.types.ts    # 用户类型
│   │   ├── translation.types.ts # 翻译类型
│   │   ├── analytics.types.ts # 分析类型
│   │   └── common.types.ts  # 通用类型
│   ├── styles/              # 样式文件
│   │   ├── globals.css      # 全局样式
│   │   ├── variables.css    # CSS变量
│   │   ├── components.css   # 组件样式
│   │   └── utilities.css    # 工具样式
│   └── assets/              # 静态资源
│       ├── images/          # 图片资源
│       ├── icons/           # 图标资源
│       └── fonts/           # 字体资源
├── .env                     # 环境变量
├── .env.example             # 环境变量模板
├── package.json             # 项目配置
├── package-lock.json        # 依赖锁定
├── tsconfig.json            # TypeScript配置
├── tsconfig.node.json       # Node.js TypeScript配置
├── vite.config.ts           # Vite配置
├── tailwind.config.js       # Tailwind配置
├── postcss.config.js        # PostCSS配置
├── .eslintrc.cjs            # ESLint配置
├── .gitignore               # Git忽略文件
├── Dockerfile               # Docker镜像
└── README.md                # 项目说明
```

### 客户前端结构
```
frontend-client/
├── (与管理后台结构类似)
├── src/
│   ├── pages/               # 页面组件
│   │   ├── HomePage/        # 首页
│   │   ├── TranslatePage/   # 翻译页面
│   │   ├── HistoryPage/     # 历史记录页面
│   │   ├── ProfilePage/     # 个人资料页面
│   │   ├── SubscriptionPage/ # 订阅管理页面
│   │   ├── LoginPage/       # 登录页面
│   │   ├── RegisterPage/    # 注册页面
│   │   └── PaymentPage/     # 支付页面
│   └── components/          # 组件库
│       ├── translation/     # 翻译相关组件
│       │   ├── TranslationInput/
│       │   ├── TranslationOutput/
│       │   ├── LanguageSelector/
│       │   └── TranslationHistory/
│       └── subscription/    # 订阅相关组件
│           ├── PlanCard/
│           ├── PaymentForm/
│           └── UsageChart/
```

## 📦 共享代码库结构

### shared/ 目录结构
```
shared/
├── types/                   # 共享类型定义
│   ├── index.ts            # 类型导出
│   ├── api.types.ts        # API通用类型
│   ├── user.types.ts       # 用户相关类型
│   ├── translation.types.ts # 翻译相关类型
│   ├── payment.types.ts    # 支付相关类型
│   ├── websocket.types.ts  # WebSocket类型
│   └── common.types.ts     # 通用类型
├── utils/                  # 共享工具函数
│   ├── index.ts           # 工具导出
│   ├── validation.ts      # 验证工具
│   ├── format.ts          # 格式化工具
│   ├── date.ts            # 日期工具
│   ├── crypto.ts          # 加密工具
│   └── constants.ts       # 通用常量
├── constants/             # 共享常量
│   ├── index.ts          # 常量导出
│   ├── api.constants.ts  # API常量
│   ├── error.constants.ts # 错误常量
│   ├── role.constants.ts # 角色常量
│   └── translation.constants.ts # 翻译常量
└── package.json          # 共享包配置
```

## 📚 文档目录结构

### docs/ 目录结构
```
docs/
├── README.md              # 文档索引
├── api/                   # API文档
│   ├── README.md         # API文档说明
│   ├── authentication.md # 认证接口文档
│   ├── users.md          # 用户接口文档
│   ├── translations.md   # 翻译接口文档
│   ├── payments.md       # 支付接口文档
│   └── websocket.md      # WebSocket文档
├── architecture/          # 架构文档
│   ├── overview.md       # 架构概览
│   ├── database.md       # 数据库设计
│   ├── security.md       # 安全设计
│   └── deployment.md     # 部署架构
├── development/           # 开发文档
│   ├── setup.md          # 环境搭建
│   ├── coding-standards.md # 编码规范
│   ├── testing.md        # 测试指南
│   └── contributing.md   # 贡献指南
├── deployment/            # 部署文档
│   ├── docker.md         # Docker部署
│   ├── production.md     # 生产环境部署
│   └── monitoring.md     # 监控配置
└── user-guides/           # 用户指南
    ├── admin-guide.md    # 管理员指南
    ├── user-guide.md     # 用户指南
    └── troubleshooting.md # 故障排除
```

## 🔧 脚本目录结构

### scripts/ 目录结构
```
scripts/
├── build/                 # 构建脚本
│   ├── build-all.sh      # 构建所有项目
│   ├── build-backend.sh  # 构建后端
│   ├── build-frontend.sh # 构建前端
│   └── clean.sh          # 清理构建文件
├── deploy/                # 部署脚本
│   ├── deploy-dev.sh     # 开发环境部署
│   ├── deploy-prod.sh    # 生产环境部署
│   ├── rollback.sh       # 回滚脚本
│   └── health-check.sh   # 健康检查
├── database/              # 数据库脚本
│   ├── migrate.sh        # 数据库迁移
│   ├── seed.sh           # 数据种子
│   ├── backup.sh         # 数据备份
│   └── restore.sh        # 数据恢复
├── development/           # 开发脚本
│   ├── dev-setup.sh      # 开发环境设置
│   ├── install-deps.sh   # 安装依赖
│   ├── lint.sh           # 代码检查
│   └── test.sh           # 运行测试
└── utils/                 # 工具脚本
    ├── generate-ssl.sh   # 生成SSL证书
    ├── update-deps.sh    # 更新依赖
    └── check-ports.sh    # 检查端口占用
```

## 🧪 测试目录结构

### tests/ 目录结构
```
tests/
├── e2e/                   # 端到端测试
│   ├── specs/            # 测试规范
│   │   ├── auth.e2e.ts   # 认证测试
│   │   ├── translation.e2e.ts # 翻译测试
│   │   ├── payment.e2e.ts # 支付测试
│   │   └── admin.e2e.ts  # 管理测试
│   ├── fixtures/         # 测试数据
│   │   ├── users.json    # 用户测试数据
│   │   └── translations.json # 翻译测试数据
│   ├── support/          # 测试支持
│   │   ├── commands.ts   # 自定义命令
│   │   └── helpers.ts    # 测试辅助函数
│   └── cypress.config.ts # Cypress配置
├── performance/           # 性能测试
│   ├── load-test.js      # 负载测试
│   ├── stress-test.js    # 压力测试
│   └── benchmark.js      # 基准测试
├── security/              # 安全测试
│   ├── auth-test.js      # 认证安全测试
│   ├── injection-test.js # 注入攻击测试
│   └── xss-test.js       # XSS测试
└── utils/                 # 测试工具
    ├── test-db.ts        # 测试数据库
    ├── mock-server.ts    # Mock服务器
    └── test-helpers.ts   # 测试辅助函数
```

## 🚀 部署配置结构

### deployment/ 目录结构
```
deployment/
├── docker/                # Docker配置
│   ├── Dockerfile.backend # 后端Docker文件
│   ├── Dockerfile.frontend-admin # 管理后台Docker文件
│   ├── Dockerfile.frontend-client # 客户前端Docker文件
│   └── docker-compose.yml # Docker编排文件
├── kubernetes/            # Kubernetes配置
│   ├── namespace.yaml    # 命名空间
│   ├── configmap.yaml    # 配置映射
│   ├── secret.yaml       # 密钥配置
│   ├── deployment.yaml   # 部署配置
│   ├── service.yaml      # 服务配置
│   └── ingress.yaml      # 入口配置
├── nginx/                 # Nginx配置
│   ├── nginx.conf        # 主配置文件
│   ├── sites-available/  # 站点配置
│   │   ├── admin.conf    # 管理后台配置
│   │   └── client.conf   # 客户前端配置
│   └── ssl/              # SSL证书
├── monitoring/            # 监控配置
│   ├── prometheus.yml    # Prometheus配置
│   ├── grafana/          # Grafana配置
│   │   ├── dashboards/   # 仪表盘配置
│   │   └── datasources/  # 数据源配置
│   └── alertmanager.yml  # 告警配置
└── ci-cd/                 # CI/CD配置
    ├── .github/          # GitHub Actions
    │   └── workflows/    # 工作流配置
    ├── gitlab-ci.yml     # GitLab CI配置
    └── jenkins/          # Jenkins配置
```

## 📋 文件命名规范

### 通用命名规范
- **文件名**: 使用kebab-case (小写字母+连字符)
- **目录名**: 使用kebab-case或camelCase
- **组件文件**: 使用PascalCase
- **类型文件**: 以.types.ts结尾
- **测试文件**: 以.spec.ts或.test.ts结尾
- **配置文件**: 以.config.ts结尾

### 示例
```
✅ 正确命名
user-service.ts
UserProfile.tsx
api.types.ts
user.spec.ts
database.config.ts

❌ 错误命名
UserService.ts
userprofile.tsx
apiTypes.ts
userTest.ts
databaseConfig.ts
```

## 🔄 模块依赖关系

### 依赖层级
```
┌─────────────────┐
│   Frontend      │ ← 用户界面层
├─────────────────┤
│   API Gateway   │ ← API网关层
├─────────────────┤
│   Business      │ ← 业务逻辑层
├─────────────────┤
│   Data Access   │ ← 数据访问层
├─────────────────┤
│   Database      │ ← 数据存储层
└─────────────────┘
```

### 模块间通信
- **前端 ↔ 后端**: HTTP/HTTPS + WebSocket
- **后端模块间**: 依赖注入 + 事件总线
- **数据库访问**: TypeORM + Repository模式
- **缓存访问**: Redis客户端
- **外部服务**: HTTP客户端 + 适配器模式

## 📝 开发工作流

### 新功能开发流程
1. **创建功能分支**: `git checkout -b feature/new-feature`
2. **后端开发**:
   - 创建实体 (Entity)
   - 创建DTO (Data Transfer Object)
   - 创建服务 (Service)
   - 创建控制器 (Controller)
   - 编写测试
3. **前端开发**:
   - 创建API服务
   - 创建页面组件
   - 创建业务组件
   - 集成状态管理
   - 编写测试
4. **集成测试**: 端到端测试
5. **代码审查**: Pull Request
6. **合并主分支**: 通过CI/CD部署

### 目录创建指南
```bash
# 创建新的后端模块
mkdir -p backend/src/modules/new-module/{dto,interfaces}
touch backend/src/modules/new-module/{new-module.module.ts,new-module.controller.ts,new-module.service.ts,new-module.entity.ts}

# 创建新的前端页面
mkdir -p frontend-admin/src/pages/NewPage/{components,hooks}
touch frontend-admin/src/pages/NewPage/{index.tsx,NewPage.tsx}

# 创建新的共享类型
touch shared/types/new-feature.types.ts
```

这个项目结构文档为开发团队提供了清晰的项目组织指南，确保代码的可维护性和扩展性。通过统一的目录结构和命名规范，团队成员可以快速定位和理解代码，提高开发效率。
