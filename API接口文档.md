# SynText 实时翻译系统 - API接口文档

## 📋 文档信息

| 项目名称 | SynText 实时翻译系统 |
|---------|-------------------|
| 版本 | 2.0 (重构版) |
| 文档版本 | v1.0 |
| 创建日期 | 2025-08-03 |
| 更新日期 | 2025-08-03 |
| 负责人 | 后端团队 |

## 🌐 API概述

### 基础信息
- **Base URL**: `https://syntext.cn/api/v1`
- **协议**: HTTPS
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    timestamp: string;
    requestId: string;
  };
}
```

### 通用错误码
| 错误码 | HTTP状态码 | 描述 |
|--------|-----------|------|
| AUTH_001 | 401 | 未授权访问 |
| AUTH_002 | 401 | Token已过期 |
| AUTH_003 | 401 | Token无效 |
| VALID_001 | 400 | 请求参数验证失败 |
| RATE_001 | 429 | 请求频率超限 |
| SERVER_001 | 500 | 服务器内部错误 |

## 🔐 认证接口

### 1. 用户注册
**接口**: `POST /auth/register`

**请求参数**:
```typescript
interface RegisterRequest {
  email: string;          // 邮箱地址
  password: string;       // 密码 (8-50字符)
  firstName?: string;     // 名字 (可选)
  lastName?: string;      // 姓氏 (可选)
  username?: string;      // 用户名 (可选)
}
```

**请求示例**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "张",
  "lastName": "三",
  "username": "zhangsan"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-string",
      "email": "<EMAIL>",
      "firstName": "张",
      "lastName": "三",
      "role": "user",
      "status": "active",
      "emailVerified": false
    },
    "accessToken": "jwt-token-string",
    "refreshToken": "refresh-token-string"
  },
  "message": "注册成功"
}
```

### 2. 用户登录
**接口**: `POST /auth/login`

**请求参数**:
```typescript
interface LoginRequest {
  email: string;     // 邮箱地址
  password: string;  // 密码
}
```

**请求示例**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-string",
      "email": "<EMAIL>",
      "firstName": "张",
      "lastName": "三",
      "role": "user",
      "status": "active"
    },
    "accessToken": "jwt-token-string",
    "refreshToken": "refresh-token-string"
  },
  "message": "登录成功"
}
```

### 3. 刷新Token
**接口**: `POST /auth/refresh`

**请求参数**:
```typescript
interface RefreshRequest {
  refreshToken: string;  // 刷新Token
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "accessToken": "new-jwt-token-string",
    "refreshToken": "new-refresh-token-string"
  },
  "message": "Token刷新成功"
}
```

### 4. 用户登出
**接口**: `POST /auth/logout`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface LogoutRequest {
  refreshToken?: string;  // 可选，不提供则登出所有设备
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

## 🌍 翻译接口

### 1. 文本翻译
**接口**: `POST /translation/translate`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface TranslateRequest {
  text: string;              // 待翻译文本 (最大10000字符)
  sourceLang: string;        // 源语言代码 (如: zh, en, ja)
  targetLang: string;        // 目标语言代码
  context?: string;          // 上下文信息 (可选)
  aiProvider?: string;       // AI提供商 (可选: deepseek, volcengine)
}
```

**请求示例**:
```json
{
  "text": "Hello, how are you?",
  "sourceLang": "en",
  "targetLang": "zh",
  "context": "日常对话",
  "aiProvider": "deepseek"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "translation-uuid",
    "originalText": "Hello, how are you?",
    "translatedText": "你好，你好吗？",
    "sourceLang": "en",
    "targetLang": "zh",
    "confidence": 0.95,
    "cached": false,
    "processingTime": 1200,
    "aiProvider": "deepseek",
    "createdAt": "2025-08-03T10:30:00Z"
  },
  "message": "翻译成功"
}
```

### 2. 批量翻译
**接口**: `POST /translation/batch`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface BatchTranslateRequest {
  texts: string[];           // 待翻译文本数组 (最大100条)
  sourceLang: string;        // 源语言代码
  targetLang: string;        // 目标语言代码
  context?: string;          // 上下文信息 (可选)
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "batchId": "batch-uuid",
    "results": [
      {
        "index": 0,
        "originalText": "Hello",
        "translatedText": "你好",
        "confidence": 0.98
      },
      {
        "index": 1,
        "originalText": "World",
        "translatedText": "世界",
        "confidence": 0.97
      }
    ],
    "totalCount": 2,
    "successCount": 2,
    "failedCount": 0
  },
  "message": "批量翻译完成"
}
```

### 3. 翻译历史
**接口**: `GET /translation/history`

**请求头**: `Authorization: Bearer <access-token>`

**查询参数**:
```typescript
interface HistoryQuery {
  page?: number;             // 页码 (默认: 1)
  limit?: number;            // 每页数量 (默认: 20, 最大: 100)
  sourceLang?: string;       // 源语言筛选
  targetLang?: string;       // 目标语言筛选
  search?: string;           // 搜索关键词
  startDate?: string;        // 开始日期 (ISO格式)
  endDate?: string;          // 结束日期 (ISO格式)
}
```

**请求示例**:
```
GET /translation/history?page=1&limit=20&sourceLang=en&search=hello
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "translation-uuid",
      "originalText": "Hello, world!",
      "translatedText": "你好，世界！",
      "sourceLang": "en",
      "targetLang": "zh",
      "confidence": 0.95,
      "isFavorite": false,
      "createdAt": "2025-08-03T10:30:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### 4. 支持的语言列表
**接口**: `GET /translation/languages`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "code": "zh",
      "name": "中文",
      "nativeName": "中文",
      "supported": true
    },
    {
      "code": "en",
      "name": "English",
      "nativeName": "English",
      "supported": true
    },
    {
      "code": "ja",
      "name": "Japanese",
      "nativeName": "日本語",
      "supported": true
    }
  ]
}
```

## 👤 用户接口

### 1. 获取用户信息
**接口**: `GET /user/profile`

**请求头**: `Authorization: Bearer <access-token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "张",
    "lastName": "三",
    "username": "zhangsan",
    "role": "user",
    "status": "active",
    "emailVerified": true,
    "createdAt": "2025-08-01T10:00:00Z",
    "lastLoginAt": "2025-08-03T09:00:00Z",
    "profile": {
      "avatar": "https://example.com/avatar.jpg",
      "timezone": "Asia/Shanghai",
      "language": "zh-CN"
    }
  }
}
```

### 2. 更新用户信息
**接口**: `PUT /user/profile`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  username?: string;
  profile?: {
    avatar?: string;
    timezone?: string;
    language?: string;
  };
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "李",
    "lastName": "四",
    "username": "lisi"
  },
  "message": "用户信息更新成功"
}
```

### 3. 获取订阅信息
**接口**: `GET /user/subscription`

**请求头**: `Authorization: Bearer <access-token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "subscription-uuid",
    "planType": "premium",
    "status": "active",
    "currentPeriodStart": "2025-08-01T00:00:00Z",
    "currentPeriodEnd": "2025-09-01T00:00:00Z",
    "usageLimit": 10000,
    "usageCount": 2500,
    "features": [
      "unlimited_translation",
      "batch_translation",
      "priority_support"
    ]
  }
}
```

### 4. 获取使用统计
**接口**: `GET /user/usage`

**请求头**: `Authorization: Bearer <access-token>`

**查询参数**:
```typescript
interface UsageQuery {
  period?: 'day' | 'week' | 'month' | 'year';  // 统计周期 (默认: month)
  startDate?: string;                          // 开始日期
  endDate?: string;                           // 结束日期
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "period": "month",
    "totalTranslations": 2500,
    "totalCharacters": 125000,
    "averageConfidence": 0.94,
    "languagePairs": [
      {
        "sourceLang": "en",
        "targetLang": "zh",
        "count": 1500
      },
      {
        "sourceLang": "zh",
        "targetLang": "en",
        "count": 1000
      }
    ],
    "dailyUsage": [
      {
        "date": "2025-08-01",
        "count": 150
      },
      {
        "date": "2025-08-02",
        "count": 200
      }
    ]
  }
}
```

## 💳 支付接口

### 1. 获取订阅套餐
**接口**: `GET /payment/plans`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "free",
      "name": "免费版",
      "price": 0,
      "currency": "CNY",
      "interval": "month",
      "features": [
        "1000次翻译/月",
        "基础翻译质量",
        "邮件支持"
      ],
      "usageLimit": 1000
    },
    {
      "id": "premium",
      "name": "高级版",
      "price": 99,
      "currency": "CNY",
      "interval": "month",
      "features": [
        "无限翻译",
        "高质量翻译",
        "批量翻译",
        "优先支持"
      ],
      "usageLimit": -1
    }
  ]
}
```

### 2. 创建订阅
**接口**: `POST /payment/subscribe`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface SubscribeRequest {
  planId: string;           // 套餐ID
  couponCode?: string;      // 优惠码 (可选)
  paymentMethod: string;    // 支付方式
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "subscriptionId": "subscription-uuid",
    "paymentUrl": "https://payment.example.com/pay/xxx",
    "amount": 99,
    "currency": "CNY",
    "status": "pending"
  },
  "message": "订阅创建成功，请完成支付"
}
```

### 3. 验证优惠码
**接口**: `POST /payment/validate-coupon`

**请求头**: `Authorization: Bearer <access-token>`

**请求参数**:
```typescript
interface ValidateCouponRequest {
  couponCode: string;       // 优惠码
  planId: string;          // 套餐ID
  amount: number;          // 原价
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "discountType": "percentage",
    "discountValue": 20,
    "discountAmount": 19.8,
    "finalAmount": 79.2,
    "message": "优惠码有效，享受8折优惠"
  }
}
```

## 🔧 管理接口

### 1. 获取系统统计
**接口**: `GET /admin/stats`

**请求头**: `Authorization: Bearer <admin-token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 10000,
      "active": 8500,
      "new": 150
    },
    "translations": {
      "total": 500000,
      "today": 2500,
      "avgConfidence": 0.94
    },
    "revenue": {
      "total": 50000,
      "thisMonth": 8500,
      "growth": 15.5
    },
    "system": {
      "uptime": "99.9%",
      "avgResponseTime": 1200,
      "errorRate": 0.1
    }
  }
}
```

### 2. 用户管理
**接口**: `GET /admin/users`

**请求头**: `Authorization: Bearer <admin-token>`

**查询参数**:
```typescript
interface AdminUsersQuery {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "firstName": "张",
      "lastName": "三",
      "role": "user",
      "status": "active",
      "subscription": {
        "planType": "premium",
        "status": "active"
      },
      "createdAt": "2025-08-01T10:00:00Z",
      "lastLoginAt": "2025-08-03T09:00:00Z"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 10000,
      "totalPages": 500
    }
  }
}
```

## 📊 WebSocket事件

### 连接认证
```typescript
// 客户端发送
socket.emit('authenticate', {
  token: 'jwt-token-string'
});

// 服务端响应
socket.on('authenticated', (data) => {
  console.log('认证成功:', data.userId);
});
```

### 实时翻译
```typescript
// 客户端发送翻译请求
socket.emit('translate', {
  text: 'Hello world',
  sourceLang: 'en',
  targetLang: 'zh'
});

// 服务端发送翻译进度
socket.on('translation_progress', (data) => {
  console.log('翻译进度:', data.progress);
});

// 服务端发送翻译结果
socket.on('translation_result', (data) => {
  console.log('翻译结果:', data.translatedText);
});
```

## 🚨 错误处理

### 常见错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALID_001",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  },
  "meta": {
    "timestamp": "2025-08-03T10:30:00Z",
    "requestId": "req-uuid"
  }
}
```

### 频率限制
```json
{
  "success": false,
  "error": {
    "code": "RATE_001",
    "message": "请求频率超限，请稍后再试"
  },
  "meta": {
    "retryAfter": 60,
    "limit": 100,
    "remaining": 0,
    "resetTime": "2025-08-03T10:31:00Z"
  }
}
```

## 📝 使用说明

### 认证流程
1. 用户注册或登录获取Token
2. 在请求头中添加 `Authorization: Bearer <token>`
3. Token过期时使用refresh接口刷新
4. 登出时调用logout接口清理Token

### 分页查询
- 使用 `page` 和 `limit` 参数进行分页
- 响应中包含 `meta.pagination` 信息
- 默认每页20条，最大100条

### 错误处理
- 所有错误都包含统一的错误码和描述
- 客户端应根据错误码进行相应处理
- 5xx错误建议重试，4xx错误需要修正请求

### 开发建议
- 使用TypeScript类型定义确保类型安全
- 实现统一的HTTP客户端封装
- 添加请求重试机制
- 实现Token自动刷新
- 添加请求/响应拦截器进行统一处理

### 测试建议
- 使用Postman或类似工具进行API测试
- 编写自动化集成测试
- 模拟各种错误场景
- 测试并发请求处理
- 验证数据格式和类型

### 版本管理
- API版本通过URL路径管理 (/api/v1/, /api/v2/)
- 向后兼容原则，新版本不破坏旧版本
- 废弃接口提前通知，给出迁移指南
- 维护版本变更日志

这个API文档为前端开发和第三方集成提供了完整的接口规范，确保开发团队能够高效地进行接口对接和功能实现。
