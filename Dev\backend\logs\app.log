{"timestamp":"2025-08-02T17:18:04.165Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"3a6cdfae-f28b-4ab8-b6f2-72d48d42452f","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:18:05.611Z","level":"info","message":"API Call: GET /api/v1/health - 500 (1446ms)","correlationId":"3a6cdfae-f28b-4ab8-b6f2-72d48d42452f","environment":"development","method":"GET","url":"/api/v1/health","statusCode":500,"duration":1446,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-02T17:18:05.613Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 1446ms","correlationId":"3a6cdfae-f28b-4ab8-b6f2-72d48d42452f","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":1446,"type":"performance","statusCode":500}
{"timestamp":"2025-08-02T17:18:58.929Z","level":"info","message":"API Call: GET /api/v1/health/live - 0 (0ms)","correlationId":"a27011ce-5ae4-4f55-a2af-0712af626798","environment":"development","method":"GET","url":"/api/v1/health/live","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:18:58.933Z","level":"info","message":"API Call: GET /api/v1/health/live - 200 (4ms)","correlationId":"a27011ce-5ae4-4f55-a2af-0712af626798","environment":"development","method":"GET","url":"/api/v1/health/live","statusCode":200,"duration":4,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"190"}
{"timestamp":"2025-08-02T17:20:04.366Z","level":"info","message":"API Call: GET /api/v1/health/ready - 0 (0ms)","correlationId":"bdffe1bc-f5d4-49b4-a6f2-25154f8a0821","environment":"development","method":"GET","url":"/api/v1/health/ready","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:20:04.561Z","level":"info","message":"API Call: GET /api/v1/health/ready - 500 (195ms)","correlationId":"bdffe1bc-f5d4-49b4-a6f2-25154f8a0821","environment":"development","method":"GET","url":"/api/v1/health/ready","statusCode":500,"duration":195,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-02T17:24:41.520Z","level":"info","message":"API Call: GET /api/v1/health/ready - 0 (0ms)","correlationId":"75527f46-8507-4778-a071-6c49ecc7226f","environment":"development","method":"GET","url":"/api/v1/health/ready","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:24:45.685Z","level":"info","message":"API Call: GET /api/v1/health/ready - 503 (4167ms)","correlationId":"75527f46-8507-4778-a071-6c49ecc7226f","environment":"development","method":"GET","url":"/api/v1/health/ready","statusCode":503,"duration":4167,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"668"}
{"timestamp":"2025-08-02T17:24:45.686Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health/ready completed in 4167ms","correlationId":"75527f46-8507-4778-a071-6c49ecc7226f","environment":"development","operation":"Slow API Response: GET /api/v1/health/ready","duration":4167,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:25:26.364Z","level":"info","message":"API Call: GET /api/v1/health/metrics - 0 (0ms)","correlationId":"d688f3b6-d343-4cac-b53b-b741b97d57b2","environment":"development","method":"GET","url":"/api/v1/health/metrics","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:25:26.408Z","level":"info","message":"API Call: GET /api/v1/health/metrics - 200 (44ms)","correlationId":"d688f3b6-d343-4cac-b53b-b741b97d57b2","environment":"development","method":"GET","url":"/api/v1/health/metrics","statusCode":200,"duration":44,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"317"}
{"timestamp":"2025-08-02T17:43:28.062Z","level":"info","message":"API Call: POST /api/v1/translation/translate - 0 (0ms)","correlationId":"b8ccab4e-2da6-42f4-ab22-863ffee33e17","environment":"development","method":"POST","url":"/api/v1/translation/translate","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:43:28.069Z","level":"info","message":"API Call: POST /api/v1/translation/translate - 404 (7ms)","correlationId":"b8ccab4e-2da6-42f4-ab22-863ffee33e17","environment":"development","method":"POST","url":"/api/v1/translation/translate","statusCode":404,"duration":7,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"186"}
{"timestamp":"2025-08-02T17:44:03.105Z","level":"info","message":"API Call: POST /api/v1/ai/translate - 0 (0ms)","correlationId":"4e50340d-4c24-4f70-8e50-cd2842f0f8f2","environment":"development","method":"POST","url":"/api/v1/ai/translate","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:44:07.883Z","level":"info","message":"API Call: POST /api/v1/ai/translate - 201 (4778ms)","correlationId":"4e50340d-4c24-4f70-8e50-cd2842f0f8f2","environment":"development","method":"POST","url":"/api/v1/ai/translate","statusCode":201,"duration":4778,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"215"}
{"timestamp":"2025-08-02T17:44:07.884Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/translate completed in 4778ms","correlationId":"4e50340d-4c24-4f70-8e50-cd2842f0f8f2","environment":"development","operation":"Slow API Response: POST /api/v1/ai/translate","duration":4778,"type":"performance","statusCode":201}
{"timestamp":"2025-08-02T17:44:18.190Z","level":"info","message":"API Call: POST /api/v1/ai/translate - 0 (0ms)","correlationId":"fc303d49-eb1c-47fd-9345-64a1a173b00b","environment":"development","method":"POST","url":"/api/v1/ai/translate","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:44:18.195Z","level":"info","message":"API Call: POST /api/v1/ai/translate - 201 (5ms)","correlationId":"fc303d49-eb1c-47fd-9345-64a1a173b00b","environment":"development","method":"POST","url":"/api/v1/ai/translate","statusCode":201,"duration":5,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"203"}
{"timestamp":"2025-08-02T17:44:28.029Z","level":"info","message":"API Call: GET /api/v1/ai/health - 0 (0ms)","correlationId":"a7d614b0-8515-4635-b5a1-f4b7143cc4be","environment":"development","method":"GET","url":"/api/v1/ai/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T17:44:28.032Z","level":"info","message":"API Call: GET /api/v1/ai/health - 200 (3ms)","correlationId":"a7d614b0-8515-4635-b5a1-f4b7143cc4be","environment":"development","method":"GET","url":"/api/v1/ai/health","statusCode":200,"duration":3,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"262"}
{"timestamp":"2025-08-02T17:46:28.990Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"7911fa76-869a-4e5a-8997-533f5f654d33","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:28.994Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (5ms)","correlationId":"7911fa76-869a-4e5a-8997-533f5f654d33","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":5,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:46:28.995Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"06ea02d5-63e8-4b70-9b19-596e4fc7a017","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:29.035Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"5aa21fcf-6088-4db6-9275-46a79ca9e7a7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:29.037Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"5aa21fcf-6088-4db6-9275-46a79ca9e7a7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:46:33.283Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4288ms)","correlationId":"06ea02d5-63e8-4b70-9b19-596e4fc7a017","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4288,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:33.292Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4288ms","correlationId":"06ea02d5-63e8-4b70-9b19-596e4fc7a017","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4288,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:46:33.305Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"657940ba-f3b4-4149-9e5c-37c25bd0ed10","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:36.553Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3248ms)","correlationId":"657940ba-f3b4-4149-9e5c-37c25bd0ed10","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3248,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:46:36.554Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3248ms","correlationId":"657940ba-f3b4-4149-9e5c-37c25bd0ed10","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3248,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:02.026Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"a5c5871b-0b90-4704-ac79-dc9044e8c6a7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:02.028Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"a5c5871b-0b90-4704-ac79-dc9044e8c6a7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:02.029Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"4770b980-ab42-42ca-b1bd-ab6ab792c084","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:02.043Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"ae8e1c49-f798-407c-8381-d306c418d8a9","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:02.045Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"ae8e1c49-f798-407c-8381-d306c418d8a9","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:02.108Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"b1ea5ad8-bd4b-41a6-b9bc-db2430efa5e6","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:02.109Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (1ms)","correlationId":"b1ea5ad8-bd4b-41a6-b9bc-db2430efa5e6","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":1,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:02.111Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"27861502-5b3d-4938-ac16-a339d40ef690","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:02.127Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"b60a1137-0c9d-4e7a-906b-f71218bf3871","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:02.128Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (1ms)","correlationId":"b60a1137-0c9d-4e7a-906b-f71218bf3871","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":1,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:02.129Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"c51dba41-713d-4c15-bc12-8d316527c20a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:06.501Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4472ms)","correlationId":"4770b980-ab42-42ca-b1bd-ab6ab792c084","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4472,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:06.502Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4472ms","correlationId":"4770b980-ab42-42ca-b1bd-ab6ab792c084","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4472,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:06.502Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4373ms)","correlationId":"c51dba41-713d-4c15-bc12-8d316527c20a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4373,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:06.503Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4373ms","correlationId":"c51dba41-713d-4c15-bc12-8d316527c20a","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4373,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:06.504Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"9d9ea45f-e0df-4927-aae5-f1c2a97aca80","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:06.517Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4406ms)","correlationId":"27861502-5b3d-4938-ac16-a339d40ef690","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4406,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:06.517Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4406ms","correlationId":"27861502-5b3d-4938-ac16-a339d40ef690","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4406,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:10.141Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3637ms)","correlationId":"9d9ea45f-e0df-4927-aae5-f1c2a97aca80","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3637,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:10.142Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3637ms","correlationId":"9d9ea45f-e0df-4927-aae5-f1c2a97aca80","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3637,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:24.026Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"3100ca79-4ff3-4e71-87ff-30b120bdce71","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:24.028Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"3100ca79-4ff3-4e71-87ff-30b120bdce71","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:24.031Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"1a3a61ee-e1b1-4cf1-8f6d-cd0743062cef","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:24.045Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"a8ec67de-7826-4fb3-9aec-ba012018632a","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:24.047Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"a8ec67de-7826-4fb3-9aec-ba012018632a","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:24.444Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"ecf9ee36-86be-4992-be53-747892118c37","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:24.445Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (1ms)","correlationId":"ecf9ee36-86be-4992-be53-747892118c37","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":1,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:24.447Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"56178450-a3eb-4420-83dc-ddeb97af1fac","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:24.455Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"aa0894c0-ad99-4114-97cc-0274ee5403f3","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:24.457Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"aa0894c0-ad99-4114-97cc-0274ee5403f3","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","responseSize":"130"}
{"timestamp":"2025-08-02T17:53:24.458Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"0f9e753e-120d-4df6-8ec1-0499b55fcb1b","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:28.051Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4020ms)","correlationId":"1a3a61ee-e1b1-4cf1-8f6d-cd0743062cef","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4020,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:28.052Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4020ms","correlationId":"1a3a61ee-e1b1-4cf1-8f6d-cd0743062cef","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4020,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:28.055Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"cf4d2826-8f6b-46af-bf10-7884f9a2c51d","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:28.491Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4033ms)","correlationId":"0f9e753e-120d-4df6-8ec1-0499b55fcb1b","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4033,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:28.491Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4033ms","correlationId":"0f9e753e-120d-4df6-8ec1-0499b55fcb1b","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4033,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:28.492Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4046ms)","correlationId":"56178450-a3eb-4420-83dc-ddeb97af1fac","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4046,"type":"api","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"}
{"timestamp":"2025-08-02T17:53:28.492Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4046ms","correlationId":"56178450-a3eb-4420-83dc-ddeb97af1fac","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4046,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:53:31.505Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3449ms)","correlationId":"cf4d2826-8f6b-46af-bf10-7884f9a2c51d","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3449,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:53:31.505Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3449ms","correlationId":"cf4d2826-8f6b-46af-bf10-7884f9a2c51d","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3449,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:59:07.966Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"0dfb8679-ba25-4d2e-bc66-cdd54eb8d939","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:07.974Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (8ms)","correlationId":"0dfb8679-ba25-4d2e-bc66-cdd54eb8d939","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":8,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:59:07.975Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"0ad429cf-626d-45c2-9ff0-95ae171f0226","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:08.002Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"ba604a3f-346c-4ecc-a99b-035a0c57561d","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:08.005Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"ba604a3f-346c-4ecc-a99b-035a0c57561d","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:59:12.223Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4248ms)","correlationId":"0ad429cf-626d-45c2-9ff0-95ae171f0226","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4248,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:12.224Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4248ms","correlationId":"0ad429cf-626d-45c2-9ff0-95ae171f0226","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4248,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:59:12.225Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"0ceba3c6-4b8f-45bf-bac0-c71facee7030","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:15.693Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3469ms)","correlationId":"0ceba3c6-4b8f-45bf-bac0-c71facee7030","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3469,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:15.694Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3469ms","correlationId":"0ceba3c6-4b8f-45bf-bac0-c71facee7030","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3469,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:59:18.695Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"01a0f1fe-c2d9-4f41-ac1e-7a126ab6fc25","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:18.697Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"01a0f1fe-c2d9-4f41-ac1e-7a126ab6fc25","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:59:18.698Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"5cb07bc1-124a-4e32-af81-8e2eeee3cc59","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:18.721Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"f7471ad6-313f-43ee-b787-3b7ef43f6bf5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:18.724Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"f7471ad6-313f-43ee-b787-3b7ef43f6bf5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T17:59:22.223Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3525ms)","correlationId":"5cb07bc1-124a-4e32-af81-8e2eeee3cc59","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3525,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:22.226Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3525ms","correlationId":"5cb07bc1-124a-4e32-af81-8e2eeee3cc59","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3525,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T17:59:22.227Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"1522c569-b79f-4587-a767-81051e2288cc","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:25.347Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3120ms)","correlationId":"1522c569-b79f-4587-a767-81051e2288cc","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3120,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T17:59:25.348Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3120ms","correlationId":"1522c569-b79f-4587-a767-81051e2288cc","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3120,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:00:33.554Z","level":"info","message":"API Call: GET /api/v1/admin/users?page=1&limit=10 - 0 (0ms)","correlationId":"eacf3415-d1ba-4035-9561-a503797b671a","environment":"development","method":"GET","url":"/api/v1/admin/users?page=1&limit=10","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:33.558Z","level":"info","message":"API Call: GET /api/v1/admin/users?page=1&limit=10 - 401 (3ms)","correlationId":"eacf3415-d1ba-4035-9561-a503797b671a","environment":"development","method":"GET","url":"/api/v1/admin/users?page=1&limit=10","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"142"}
{"timestamp":"2025-08-02T18:00:33.560Z","level":"info","message":"API Call: GET /api/v1/admin/users?page=1&limit=10 - 0 (0ms)","correlationId":"3e205668-9851-461d-b97a-77d5c901a9f9","environment":"development","method":"GET","url":"/api/v1/admin/users?page=1&limit=10","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:33.563Z","level":"info","message":"API Call: GET /api/v1/admin/users?page=1&limit=10 - 401 (3ms)","correlationId":"3e205668-9851-461d-b97a-77d5c901a9f9","environment":"development","method":"GET","url":"/api/v1/admin/users?page=1&limit=10","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"142"}
{"timestamp":"2025-08-02T18:00:38.409Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"168aa4f2-ea4d-45d9-b9e3-f6ae5a6df9d9","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:38.411Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"168aa4f2-ea4d-45d9-b9e3-f6ae5a6df9d9","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:00:38.412Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"8635cb6f-9cea-4f40-878b-c88e6cbd9294","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:38.415Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"8635cb6f-9cea-4f40-878b-c88e6cbd9294","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:00:38.419Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"112152b9-3c64-477f-9fc6-964744b958f4","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:40.335Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"acb90bb4-9c58-4428-a56e-6f43a5cbde6e","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:40.338Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"acb90bb4-9c58-4428-a56e-6f43a5cbde6e","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:00:40.341Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"e7113cd6-e46a-4837-80f7-c31f61f8ca05","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:40.344Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"e7113cd6-e46a-4837-80f7-c31f61f8ca05","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:00:42.794Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4375ms)","correlationId":"112152b9-3c64-477f-9fc6-964744b958f4","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4375,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:42.795Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4375ms","correlationId":"112152b9-3c64-477f-9fc6-964744b958f4","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4375,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:00:42.796Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"3af7aa24-cbdb-44cc-ba61-295aca5c702b","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:45.285Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 0 (0ms)","correlationId":"106ea4d6-9ea9-48a8-a7e9-767104fcfb56","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:45.289Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 401 (4ms)","correlationId":"106ea4d6-9ea9-48a8-a7e9-767104fcfb56","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":401,"duration":4,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"135"}
{"timestamp":"2025-08-02T18:00:45.292Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 0 (0ms)","correlationId":"f3db274b-0f02-4e10-aee9-edfc5ca9db29","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:45.295Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 401 (3ms)","correlationId":"f3db274b-0f02-4e10-aee9-edfc5ca9db29","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"141"}
{"timestamp":"2025-08-02T18:00:45.297Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 0 (0ms)","correlationId":"d221d480-b88a-4f64-9c05-4678a0a756ac","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:45.299Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 401 (3ms)","correlationId":"d221d480-b88a-4f64-9c05-4678a0a756ac","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"135"}
{"timestamp":"2025-08-02T18:00:45.301Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 0 (0ms)","correlationId":"be71499b-5f6d-45d9-a5ac-aaf0a8a1ad5d","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:45.304Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 401 (3ms)","correlationId":"be71499b-5f6d-45d9-a5ac-aaf0a8a1ad5d","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"141"}
{"timestamp":"2025-08-02T18:00:46.425Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3629ms)","correlationId":"3af7aa24-cbdb-44cc-ba61-295aca5c702b","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3629,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:46.426Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3629ms","correlationId":"3af7aa24-cbdb-44cc-ba61-295aca5c702b","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3629,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:00:46.427Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"7e5956aa-f7c8-4ff9-a5c3-f2c1ea2083b4","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:49.850Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3423ms)","correlationId":"7e5956aa-f7c8-4ff9-a5c3-f2c1ea2083b4","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3423,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:49.850Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3423ms","correlationId":"7e5956aa-f7c8-4ff9-a5c3-f2c1ea2083b4","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3423,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:00:49.851Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"ef307826-234c-4010-9eb0-27ee1e0f360a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:52.956Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3105ms)","correlationId":"ef307826-234c-4010-9eb0-27ee1e0f360a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3105,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:00:52.957Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3105ms","correlationId":"ef307826-234c-4010-9eb0-27ee1e0f360a","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3105,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:01:15.291Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 0 (0ms)","correlationId":"f09e445c-7fab-4e65-983f-4e25424eb0f1","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:15.293Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 401 (2ms)","correlationId":"f09e445c-7fab-4e65-983f-4e25424eb0f1","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"135"}
{"timestamp":"2025-08-02T18:01:15.294Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"68064c07-dae4-4445-9df2-5b0c77d57a89","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:19.354Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4060ms)","correlationId":"68064c07-dae4-4445-9df2-5b0c77d57a89","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4060,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:19.354Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4060ms","correlationId":"68064c07-dae4-4445-9df2-5b0c77d57a89","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4060,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:01:26.777Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 0 (0ms)","correlationId":"f4a6cf1c-3e61-4c9f-ac5b-7dc256b08193","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:26.779Z","level":"info","message":"API Call: GET /api/v1/admin/system/metrics - 401 (2ms)","correlationId":"f4a6cf1c-3e61-4c9f-ac5b-7dc256b08193","environment":"development","method":"GET","url":"/api/v1/admin/system/metrics","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"135"}
{"timestamp":"2025-08-02T18:01:26.780Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 0 (0ms)","correlationId":"66fb25ce-eeb1-4c08-be04-a2158c629611","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:26.782Z","level":"info","message":"API Call: GET /api/v1/admin/system/logs?limit=50 - 401 (2ms)","correlationId":"66fb25ce-eeb1-4c08-be04-a2158c629611","environment":"development","method":"GET","url":"/api/v1/admin/system/logs?limit=50","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"141"}
{"timestamp":"2025-08-02T18:01:26.783Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"d4110a89-4497-41c4-a21f-be3617349da7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:30.533Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3749ms)","correlationId":"d4110a89-4497-41c4-a21f-be3617349da7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3749,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:01:30.534Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3749ms","correlationId":"d4110a89-4497-41c4-a21f-be3617349da7","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3749,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:22:00.461Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"92ddbeee-fb62-4210-9a8f-0253b60701da","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:00.517Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (64ms)","correlationId":"92ddbeee-fb62-4210-9a8f-0253b60701da","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":64,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:22:00.519Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"b5421105-7302-4a17-a7e9-ae06ae19631b","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:00.526Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (7ms)","correlationId":"b5421105-7302-4a17-a7e9-ae06ae19631b","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":7,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:22:01.036Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"33cf7ca5-dc7c-47d5-8135-dabfe7d64cab","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:01.041Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (5ms)","correlationId":"33cf7ca5-dc7c-47d5-8135-dabfe7d64cab","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":5,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:22:01.044Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"1249fc65-a76b-4b0c-b456-f3a308409c5c","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:01.209Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"ec557614-59eb-445c-bfeb-0ff710ef2a39","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:01.211Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (2ms)","correlationId":"ec557614-59eb-445c-bfeb-0ff710ef2a39","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-02T18:22:05.367Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4324ms)","correlationId":"1249fc65-a76b-4b0c-b456-f3a308409c5c","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4324,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:05.369Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4324ms","correlationId":"1249fc65-a76b-4b0c-b456-f3a308409c5c","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4324,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:22:05.378Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"47c7a867-fd4b-44e5-8cca-a1e2c8a48ce7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:08.716Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3338ms)","correlationId":"47c7a867-fd4b-44e5-8cca-a1e2c8a48ce7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3338,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-02T18:22:08.719Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3338ms","correlationId":"47c7a867-fd4b-44e5-8cca-a1e2c8a48ce7","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3338,"type":"performance","statusCode":503}
{"timestamp":"2025-08-02T18:41:50.954Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"6b282da0-ed92-4183-98af-22aa871bf9ae","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:41:50.962Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 404 (10ms)","correlationId":"6b282da0-ed92-4183-98af-22aa871bf9ae","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":404,"duration":10,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"162"}
{"timestamp":"2025-08-02T18:44:37.246Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"51572bc2-6fdb-481d-88f3-f2e9cb1f0589","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:44:37.259Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 401 (14ms)","correlationId":"51572bc2-6fdb-481d-88f3-f2e9cb1f0589","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":401,"duration":14,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"125"}
{"timestamp":"2025-08-02T18:44:48.784Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"8055ce18-e413-4bf9-8536-7ff10d6e70ff","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:44:48.838Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (55ms)","correlationId":"8055ce18-e413-4bf9-8536-7ff10d6e70ff","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":55,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-02T18:44:57.931Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"c31d6152-573c-451e-9dce-1662c8897c60","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:44:57.937Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (6ms)","correlationId":"c31d6152-573c-451e-9dce-1662c8897c60","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":6,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-02T18:45:06.159Z","level":"info","message":"API Call: GET /api/v1/auth/debug/users - 0 (0ms)","correlationId":"20dea414-63d2-4b2a-bf2c-8525bf389198","environment":"development","method":"GET","url":"/api/v1/auth/debug/users","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:45:06.170Z","level":"info","message":"API Call: GET /api/v1/auth/debug/users - 200 (11ms)","correlationId":"20dea414-63d2-4b2a-bf2c-8525bf389198","environment":"development","method":"GET","url":"/api/v1/auth/debug/users","statusCode":200,"duration":11,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"2747"}
{"timestamp":"2025-08-02T18:45:21.368Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"e1f5cead-9f11-44f1-9ac7-8fe458e9a4a8","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:45:21.446Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (78ms)","correlationId":"e1f5cead-9f11-44f1-9ac7-8fe458e9a4a8","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":78,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-02T18:45:34.576Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"e337d7de-03c1-4f6c-82d7-22e9ac4b4b3a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:45:34.645Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (69ms)","correlationId":"e337d7de-03c1-4f6c-82d7-22e9ac4b4b3a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":69,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-02T18:46:15.246Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"a4d19314-1a2f-4cf2-ae18-bdff0ce99ae7","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:46:15.322Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (76ms)","correlationId":"a4d19314-1a2f-4cf2-ae18-bdff0ce99ae7","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":76,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"460"}
{"timestamp":"2025-08-02T18:46:27.296Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"a5a4caa9-08b6-40bd-bab3-ea8128e5c6f4","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:46:27.318Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 400 (21ms)","correlationId":"a5a4caa9-08b6-40bd-bab3-ea8128e5c6f4","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":400,"duration":21,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"207"}
{"timestamp":"2025-08-02T18:51:18.032Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"e9488436-0acd-4a9f-8cf9-7ec6a349a89f","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:51:18.071Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (39ms)","correlationId":"e9488436-0acd-4a9f-8cf9-7ec6a349a89f","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":39,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"440"}
{"timestamp":"2025-08-02T18:55:03.431Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"2878f3fd-c5af-43cf-889b-b160552901f3","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:55:03.493Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (63ms)","correlationId":"2878f3fd-c5af-43cf-889b-b160552901f3","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":63,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"440"}
{"timestamp":"2025-08-02T18:59:56.922Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"38de677a-9671-4069-827c-6519ae24c9d0","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T18:59:57.023Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (101ms)","correlationId":"38de677a-9671-4069-827c-6519ae24c9d0","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":101,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"460"}
{"timestamp":"2025-08-02T19:00:09.400Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"3941fc32-db4f-4fbd-b0f9-e41809421947","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-02T19:00:09.426Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (26ms)","correlationId":"3941fc32-db4f-4fbd-b0f9-e41809421947","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":26,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"440"}
{"timestamp":"2025-08-03T04:08:58.714Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"ad4c3888-1f40-40d5-90cf-176c177ed8a7","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T04:08:58.760Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (47ms)","correlationId":"ad4c3888-1f40-40d5-90cf-176c177ed8a7","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":47,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"440"}
{"timestamp":"2025-08-03T04:09:38.251Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"50a56777-1a04-449e-b122-086f29a4da8d","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:09:38.335Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (84ms)","correlationId":"50a56777-1a04-449e-b122-086f29a4da8d","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":84,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:09:38.340Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"8e39f7da-6d11-4585-8b01-5d9ced366c6d","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:09:55.086Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (16746ms)","correlationId":"8e39f7da-6d11-4585-8b01-5d9ced366c6d","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":16746,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:09:55.087Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 16746ms","correlationId":"8e39f7da-6d11-4585-8b01-5d9ced366c6d","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":16746,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:09:55.094Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"cc83aa95-1c63-484b-8b0d-3715810d1ff3","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:10:15.319Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (20225ms)","correlationId":"cc83aa95-1c63-484b-8b0d-3715810d1ff3","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":20225,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:10:15.319Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 20225ms","correlationId":"cc83aa95-1c63-484b-8b0d-3715810d1ff3","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":20225,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:12:55.761Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"df536a7a-bec4-4e93-b318-89c4cc7f77cc","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:12:55.845Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (84ms)","correlationId":"df536a7a-bec4-4e93-b318-89c4cc7f77cc","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":84,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:12:55.852Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"c1cc51ea-42ec-4378-9316-814ab1592a99","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:13:11.074Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (15222ms)","correlationId":"c1cc51ea-42ec-4378-9316-814ab1592a99","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":15222,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:13:11.075Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 15222ms","correlationId":"c1cc51ea-42ec-4378-9316-814ab1592a99","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":15222,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:13:11.081Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"a23cad7b-2bae-4542-ba9f-da8325e28095","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:13:31.927Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (20846ms)","correlationId":"a23cad7b-2bae-4542-ba9f-da8325e28095","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":20846,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:13:31.927Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 20846ms","correlationId":"a23cad7b-2bae-4542-ba9f-da8325e28095","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":20846,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:15:08.166Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"a50791f5-1939-4196-91c7-3d6745719ed5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T04:15:08.174Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (8ms)","correlationId":"a50791f5-1939-4196-91c7-3d6745719ed5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":8,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T04:15:08.262Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"79b0c675-bcc6-469b-98f6-1a4b6c8982a6","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T04:15:08.265Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"79b0c675-bcc6-469b-98f6-1a4b6c8982a6","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T04:18:42.840Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"6531f14b-4058-412b-a836-845e616ee75a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:18:42.995Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (155ms)","correlationId":"6531f14b-4058-412b-a836-845e616ee75a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":155,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:18:43.002Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"8cd83820-17f1-40ad-bc61-281f8db8ef80","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:18:43.015Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (13ms)","correlationId":"8cd83820-17f1-40ad-bc61-281f8db8ef80","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":13,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"447"}
{"timestamp":"2025-08-03T04:24:12.794Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"1de357ed-1a4e-4b92-ba46-93e4a5a28c6a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:24:12.924Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (131ms)","correlationId":"1de357ed-1a4e-4b92-ba46-93e4a5a28c6a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":131,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:24:12.929Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"04b0ef67-fcb0-4235-9e58-a6cd16ca5587","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:24:12.940Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (11ms)","correlationId":"04b0ef67-fcb0-4235-9e58-a6cd16ca5587","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":11,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"384"}
{"timestamp":"2025-08-03T04:24:49.218Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"9d949756-97e0-467b-a0fb-85dc1b6b1383","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:24:49.288Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (70ms)","correlationId":"9d949756-97e0-467b-a0fb-85dc1b6b1383","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":70,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:24:49.293Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"06d9477a-45a3-42dd-a566-d5bc5dfbb861","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:25:03.457Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (14164ms)","correlationId":"06d9477a-45a3-42dd-a566-d5bc5dfbb861","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":14164,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:25:03.458Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 14164ms","correlationId":"06d9477a-45a3-42dd-a566-d5bc5dfbb861","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":14164,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:34:59.220Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"25675373-5f95-4c9b-ba4f-734e5dceb3d6","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:34:59.473Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (259ms)","correlationId":"25675373-5f95-4c9b-ba4f-734e5dceb3d6","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":259,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:34:59.486Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"346ed2cd-866c-4434-b6a9-6a4b8f4337b2","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:35:16.298Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (16812ms)","correlationId":"346ed2cd-866c-4434-b6a9-6a4b8f4337b2","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":16812,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:35:16.300Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 16812ms","correlationId":"346ed2cd-866c-4434-b6a9-6a4b8f4337b2","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":16812,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:38:06.657Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"b7f85c20-d526-4e53-84b6-fe0c3645329e","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:38:06.735Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (78ms)","correlationId":"b7f85c20-d526-4e53-84b6-fe0c3645329e","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":78,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:38:06.740Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"64040886-651e-4c13-9d39-6ad1fd917c40","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:38:19.754Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (13014ms)","correlationId":"64040886-651e-4c13-9d39-6ad1fd917c40","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":13014,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:38:19.754Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 13014ms","correlationId":"64040886-651e-4c13-9d39-6ad1fd917c40","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":13014,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:49:30.662Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"cb176660-8c8e-4468-9fd1-3de1493ca7a7","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:49:30.785Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (124ms)","correlationId":"cb176660-8c8e-4468-9fd1-3de1493ca7a7","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":124,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:49:30.793Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 0 (0ms)","correlationId":"b5d188c5-169d-472e-ae25-913c594b0a55","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:49:48.109Z","level":"info","message":"API Call: POST /api/v1/ai/qa/ask - 201 (17316ms)","correlationId":"b5d188c5-169d-472e-ae25-913c594b0a55","environment":"development","method":"POST","url":"/api/v1/ai/qa/ask","statusCode":201,"duration":17316,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:49:48.109Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/ai/qa/ask completed in 17316ms","correlationId":"b5d188c5-169d-472e-ae25-913c594b0a55","environment":"development","operation":"Slow API Response: POST /api/v1/ai/qa/ask","duration":17316,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T04:53:32.985Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"2ae8cbb6-97a0-4f13-a4a8-6df2839d15ec","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T04:53:36.303Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3318ms)","correlationId":"2ae8cbb6-97a0-4f13-a4a8-6df2839d15ec","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3318,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1146"}
{"timestamp":"2025-08-03T04:53:36.303Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3318ms","correlationId":"2ae8cbb6-97a0-4f13-a4a8-6df2839d15ec","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3318,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T04:54:36.491Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"65c8ff20-0319-4fb0-af10-983dd45678fa","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:54:39.759Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3268ms)","correlationId":"65c8ff20-0319-4fb0-af10-983dd45678fa","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3268,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:54:39.760Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3268ms","correlationId":"65c8ff20-0319-4fb0-af10-983dd45678fa","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3268,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T04:55:52.843Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"461792f6-77f0-44ab-a2ca-207b2fa13e7e","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:55:55.888Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3045ms)","correlationId":"461792f6-77f0-44ab-a2ca-207b2fa13e7e","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3045,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:55:55.889Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3045ms","correlationId":"461792f6-77f0-44ab-a2ca-207b2fa13e7e","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3045,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T04:55:55.906Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"589f3b8e-c1e5-4caf-b8cd-f10ceb50f11c","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:55:56.001Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (96ms)","correlationId":"589f3b8e-c1e5-4caf-b8cd-f10ceb50f11c","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":96,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"460"}
{"timestamp":"2025-08-03T04:55:56.014Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"81bd8b48-2be7-4afd-812e-38c470920ffa","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"axios/1.11.0"}
{"timestamp":"2025-08-03T04:55:56.078Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (64ms)","correlationId":"81bd8b48-2be7-4afd-812e-38c470920ffa","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":64,"type":"api","ip":"::1","userAgent":"axios/1.11.0","responseSize":"156"}
{"timestamp":"2025-08-03T08:50:50.062Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"d1ef91ac-4f1b-4680-8bac-f6fd0ba05e7a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T08:50:53.238Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3176ms)","correlationId":"d1ef91ac-4f1b-4680-8bac-f6fd0ba05e7a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3176,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T08:50:53.238Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3176ms","correlationId":"d1ef91ac-4f1b-4680-8bac-f6fd0ba05e7a","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3176,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T08:51:14.655Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"4b7293d5-9626-4c27-81df-263700af84a7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T08:51:17.603Z","level":"info","message":"API Call: GET /api/v1/health - 503 (2949ms)","correlationId":"4b7293d5-9626-4c27-81df-263700af84a7","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":2949,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T08:51:17.603Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 2949ms","correlationId":"4b7293d5-9626-4c27-81df-263700af84a7","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":2949,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T08:51:29.014Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"b4ab474b-2076-4cb4-befa-066d415de063","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T08:51:32.065Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3051ms)","correlationId":"b4ab474b-2076-4cb4-befa-066d415de063","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3051,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T08:51:32.065Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3051ms","correlationId":"b4ab474b-2076-4cb4-befa-066d415de063","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3051,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:03:42.272Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 0 (0ms)","correlationId":"9a631467-416e-4cae-850f-0bda89e9ed01","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:03:42.301Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 404 (31ms)","correlationId":"9a631467-416e-4cae-850f-0bda89e9ed01","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":404,"duration":31,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"186"}
{"timestamp":"2025-08-03T09:03:42.309Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 0 (0ms)","correlationId":"d1f9c014-a601-48c1-b302-5bafdc5f5bf7","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:03:42.312Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 404 (4ms)","correlationId":"d1f9c014-a601-48c1-b302-5bafdc5f5bf7","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":404,"duration":4,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"184"}
{"timestamp":"2025-08-03T09:03:42.315Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 0 (0ms)","correlationId":"23defee0-e146-45fb-bffe-927d6823d8b7","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:03:42.319Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 404 (4ms)","correlationId":"23defee0-e146-45fb-bffe-927d6823d8b7","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":404,"duration":4,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"186"}
{"timestamp":"2025-08-03T09:03:42.322Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 0 (0ms)","correlationId":"be23c1dc-0efa-4246-be7c-762d3cce7323","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:03:42.325Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 404 (3ms)","correlationId":"be23c1dc-0efa-4246-be7c-762d3cce7323","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":404,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"184"}
{"timestamp":"2025-08-03T09:04:51.624Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"d0d09164-c504-464e-ad09-2b349341f583","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:04:56.092Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4468ms)","correlationId":"d0d09164-c504-464e-ad09-2b349341f583","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4468,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1146"}
{"timestamp":"2025-08-03T09:04:56.093Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4468ms","correlationId":"d0d09164-c504-464e-ad09-2b349341f583","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4468,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:11:34.174Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"60e5156b-50cc-4053-82be-ac852fd9795e","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:11:38.662Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4488ms)","correlationId":"60e5156b-50cc-4053-82be-ac852fd9795e","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4488,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1148"}
{"timestamp":"2025-08-03T09:11:38.662Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4488ms","correlationId":"60e5156b-50cc-4053-82be-ac852fd9795e","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4488,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:21:10.669Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"f4f48328-e331-46ae-b1f4-9fc87bc903a5","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:21:10.780Z","level":"info","message":"API Call: POST /api/v1/auth/login - 500 (111ms)","correlationId":"f4f48328-e331-46ae-b1f4-9fc87bc903a5","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":500,"duration":111,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"52"}
{"timestamp":"2025-08-03T09:21:18.504Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"147e1636-8133-4299-a446-10ffa4a9332b","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:21:18.518Z","level":"info","message":"API Call: POST /api/v1/auth/login - 500 (14ms)","correlationId":"147e1636-8133-4299-a446-10ffa4a9332b","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":500,"duration":14,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"52"}
{"timestamp":"2025-08-03T09:23:08.449Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"5e79f589-542c-4ebd-98cb-2f9b3807db44","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:23:12.114Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3665ms)","correlationId":"5e79f589-542c-4ebd-98cb-2f9b3807db44","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3665,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1148"}
{"timestamp":"2025-08-03T09:23:12.114Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3665ms","correlationId":"5e79f589-542c-4ebd-98cb-2f9b3807db44","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3665,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:24:41.706Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"1a987b14-3bfe-4c06-8fb6-1cec11b04042","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:24:45.123Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3418ms)","correlationId":"1a987b14-3bfe-4c06-8fb6-1cec11b04042","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3418,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T09:24:45.124Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3418ms","correlationId":"1a987b14-3bfe-4c06-8fb6-1cec11b04042","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3418,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:30:53.468Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"d16892d8-0f65-4f9e-b778-021f707af732","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:30:56.992Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3525ms)","correlationId":"d16892d8-0f65-4f9e-b778-021f707af732","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3525,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1146"}
{"timestamp":"2025-08-03T09:30:56.993Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3525ms","correlationId":"d16892d8-0f65-4f9e-b778-021f707af732","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3525,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:31:42.498Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"dcc5815d-6ee6-410c-8d02-8689224e157e","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:31:42.500Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (2ms)","correlationId":"dcc5815d-6ee6-410c-8d02-8689224e157e","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"127"}
{"timestamp":"2025-08-03T09:31:45.252Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"cfb2d5fd-4570-4b1b-98bd-a33386972f40","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:31:45.254Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (2ms)","correlationId":"cfb2d5fd-4570-4b1b-98bd-a33386972f40","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"127"}
{"timestamp":"2025-08-03T09:31:47.748Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"15c1b288-00cb-4be3-9745-97ea355340e5","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T09:31:47.749Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (1ms)","correlationId":"15c1b288-00cb-4be3-9745-97ea355340e5","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":1,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"127"}
{"timestamp":"2025-08-03T09:35:11.668Z","level":"info","message":"API Call: GET /api/v1/auth/verify - 0 (0ms)","correlationId":"4556a209-6bca-47b9-90ab-107016ebfa7b","environment":"development","method":"GET","url":"/api/v1/auth/verify","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:35:11.682Z","level":"info","message":"API Call: GET /api/v1/auth/verify - 404 (15ms)","correlationId":"4556a209-6bca-47b9-90ab-107016ebfa7b","environment":"development","method":"GET","url":"/api/v1/auth/verify","statusCode":404,"duration":15,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"164"}
{"timestamp":"2025-08-03T09:35:21.007Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"82bb3faa-0d67-4546-9153-f533bbcbb467","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:35:21.012Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (5ms)","correlationId":"82bb3faa-0d67-4546-9153-f533bbcbb467","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":5,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:35:38.962Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"ab37cb84-5868-4cb6-b4c5-fb2a24974e94","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:35:39.093Z","level":"info","message":"API Call: POST /api/v1/auth/login - 500 (132ms)","correlationId":"ab37cb84-5868-4cb6-b4c5-fb2a24974e94","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":500,"duration":132,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T09:36:27.691Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"46724d7c-a4f5-454a-981d-969f511c8149","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:36:27.714Z","level":"info","message":"API Call: POST /api/v1/auth/login - 500 (23ms)","correlationId":"46724d7c-a4f5-454a-981d-969f511c8149","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":500,"duration":23,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T09:37:57.881Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"518bcf64-52d9-4b53-b368-3dbfb3cff517","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:38:01.227Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3347ms)","correlationId":"518bcf64-52d9-4b53-b368-3dbfb3cff517","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3347,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T09:38:01.229Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3347ms","correlationId":"518bcf64-52d9-4b53-b368-3dbfb3cff517","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3347,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T09:38:08.894Z","level":"info","message":"API Call: POST /api/v1/auth/register - 0 (0ms)","correlationId":"6cfa5b25-5b07-4237-b07a-4ca1cf0992c7","environment":"development","method":"POST","url":"/api/v1/auth/register","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:38:08.969Z","level":"info","message":"API Call: POST /api/v1/auth/register - 500 (75ms)","correlationId":"6cfa5b25-5b07-4237-b07a-4ca1cf0992c7","environment":"development","method":"POST","url":"/api/v1/auth/register","statusCode":500,"duration":75,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T09:43:26.368Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"87326dd5-2bb1-405c-a9f0-2922e7ee75f3","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:43:26.503Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (136ms)","correlationId":"87326dd5-2bb1-405c-a9f0-2922e7ee75f3","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":136,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-03T09:44:42.634Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"6e447448-d90e-435d-936b-0ab87538cf4f","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:44:42.726Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (93ms)","correlationId":"6e447448-d90e-435d-936b-0ab87538cf4f","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":93,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"463"}
{"timestamp":"2025-08-03T09:44:51.794Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"4813a256-5522-450c-a905-12a3c26d2722","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:44:51.801Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (7ms)","correlationId":"4813a256-5522-450c-a905-12a3c26d2722","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":7,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:45:26.230Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"119d71ec-46c5-447d-9b64-ff49bd3aa663","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:45:26.240Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (11ms)","correlationId":"119d71ec-46c5-447d-9b64-ff49bd3aa663","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":11,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:45:48.710Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"ac7cc7ac-6dc1-4668-94c9-74b236618327","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:45:48.788Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (78ms)","correlationId":"ac7cc7ac-6dc1-4668-94c9-74b236618327","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":78,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"463"}
{"timestamp":"2025-08-03T09:45:53.902Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"f08c1478-4078-4a96-adc5-7546712001d8","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:45:53.964Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (62ms)","correlationId":"f08c1478-4078-4a96-adc5-7546712001d8","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":62,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"463"}
{"timestamp":"2025-08-03T09:46:02.221Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"47b898a3-d420-4244-a793-ff1bf53ee1f0","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:46:02.223Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (2ms)","correlationId":"47b898a3-d420-4244-a793-ff1bf53ee1f0","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":2,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:46:24.864Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"daca7f31-1bf4-44c9-bf10-1d9acc8b2f06","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:46:24.867Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (3ms)","correlationId":"daca7f31-1bf4-44c9-bf10-1d9acc8b2f06","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:47:34.879Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"aa5817fe-df53-4267-af99-fc8a4839b0ec","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:47:34.973Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (94ms)","correlationId":"aa5817fe-df53-4267-af99-fc8a4839b0ec","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":94,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"463"}
{"timestamp":"2025-08-03T09:48:58.548Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"3e36bb92-446b-430c-bf8e-870ad7a2e645","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:48:58.634Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (86ms)","correlationId":"3e36bb92-446b-430c-bf8e-870ad7a2e645","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":86,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"463"}
{"timestamp":"2025-08-03T09:49:10.285Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"04728081-d248-4e12-b943-d8e11908a587","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:49:10.293Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (8ms)","correlationId":"04728081-d248-4e12-b943-d8e11908a587","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":8,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:50:48.577Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"10aeadf9-b7cd-4381-9561-185337351107","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:50:48.599Z","level":"info","message":"API Call: POST /api/v1/translation - 401 (23ms)","correlationId":"10aeadf9-b7cd-4381-9561-185337351107","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":401,"duration":23,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"127"}
{"timestamp":"2025-08-03T09:51:29.733Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"4254ce80-6651-444a-b89c-9e5c7984ba37","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:51:29.776Z","level":"info","message":"API Call: POST /api/v1/translation - 400 (44ms)","correlationId":"4254ce80-6651-444a-b89c-9e5c7984ba37","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":400,"duration":44,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"307"}
{"timestamp":"2025-08-03T09:51:50.392Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"ee6cc57d-d4bd-4a5c-af79-e4290c6e0eaa","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:51:54.206Z","level":"info","message":"API Call: POST /api/v1/translation - 500 (3814ms)","correlationId":"ee6cc57d-d4bd-4a5c-af79-e4290c6e0eaa","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":500,"duration":3814,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T09:51:54.207Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 3814ms","correlationId":"ee6cc57d-d4bd-4a5c-af79-e4290c6e0eaa","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":3814,"type":"performance","statusCode":500}
{"timestamp":"2025-08-03T09:57:05.836Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"4bed0e4f-1e4a-4ec7-9c4e-b44ae5c6d1dc","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:57:05.890Z","level":"info","message":"API Call: POST /api/v1/translation - 500 (54ms)","correlationId":"4bed0e4f-1e4a-4ec7-9c4e-b44ae5c6d1dc","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":500,"duration":54,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T09:59:37.772Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"58eb8328-d91f-4158-9190-34de08f59238","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T09:59:37.959Z","level":"info","message":"API Call: POST /api/v1/translation - 500 (188ms)","correlationId":"58eb8328-d91f-4158-9190-34de08f59238","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":500,"duration":188,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"52"}
{"timestamp":"2025-08-03T10:00:11.885Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"a3f848a0-9607-4767-a612-f26a89453663","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:00:11.971Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (87ms)","correlationId":"a3f848a0-9607-4767-a612-f26a89453663","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":87,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"286"}
{"timestamp":"2025-08-03T10:03:26.876Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"afd1086a-9f3a-4987-a6eb-00887493ca92","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:03:31.397Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (4522ms)","correlationId":"afd1086a-9f3a-4987-a6eb-00887493ca92","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":4522,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"436"}
{"timestamp":"2025-08-03T10:03:31.398Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 4522ms","correlationId":"afd1086a-9f3a-4987-a6eb-00887493ca92","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":4522,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:03:40.760Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"32cc4803-59bc-404e-9ab5-a54cf511082f","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:03:45.615Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (4856ms)","correlationId":"32cc4803-59bc-404e-9ab5-a54cf511082f","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":4856,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"409"}
{"timestamp":"2025-08-03T10:03:45.616Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 4856ms","correlationId":"32cc4803-59bc-404e-9ab5-a54cf511082f","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":4856,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:03:54.200Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"c9033bcc-7d5d-4d4f-8159-d58c6f005021","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:03:54.220Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (20ms)","correlationId":"c9033bcc-7d5d-4d4f-8159-d58c6f005021","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":20,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"406"}
{"timestamp":"2025-08-03T10:04:46.100Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"d331d7d3-8003-4545-b551-2e66dc24172b","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"node-fetch"}
{"timestamp":"2025-08-03T10:04:49.634Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (3534ms)","correlationId":"d331d7d3-8003-4545-b551-2e66dc24172b","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":3534,"type":"api","ip":"::1","userAgent":"node-fetch","responseSize":"320"}
{"timestamp":"2025-08-03T10:04:49.634Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 3534ms","correlationId":"d331d7d3-8003-4545-b551-2e66dc24172b","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":3534,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:04:49.641Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"a8ccbe8b-1872-4ad6-a9e0-896f189b04b2","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"node-fetch"}
{"timestamp":"2025-08-03T10:04:53.232Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (3591ms)","correlationId":"a8ccbe8b-1872-4ad6-a9e0-896f189b04b2","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":3591,"type":"api","ip":"::1","userAgent":"node-fetch","responseSize":"363"}
{"timestamp":"2025-08-03T10:04:53.232Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 3591ms","correlationId":"a8ccbe8b-1872-4ad6-a9e0-896f189b04b2","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":3591,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:04:53.235Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"b89a59d3-aee7-4ef7-baf4-04a4865af19a","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"node-fetch"}
{"timestamp":"2025-08-03T10:04:57.087Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (3852ms)","correlationId":"b89a59d3-aee7-4ef7-baf4-04a4865af19a","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":3852,"type":"api","ip":"::1","userAgent":"node-fetch","responseSize":"486"}
{"timestamp":"2025-08-03T10:04:57.087Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 3852ms","correlationId":"b89a59d3-aee7-4ef7-baf4-04a4865af19a","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":3852,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:04:57.090Z","level":"info","message":"API Call: POST /api/v1/translation - 0 (0ms)","correlationId":"3d66e06a-a9b4-41fd-a853-0fbbd0a2227a","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"node-fetch"}
{"timestamp":"2025-08-03T10:05:00.398Z","level":"info","message":"API Call: POST /api/v1/translation - 201 (3308ms)","correlationId":"3d66e06a-a9b4-41fd-a853-0fbbd0a2227a","environment":"development","method":"POST","url":"/api/v1/translation","statusCode":201,"duration":3308,"type":"api","ip":"::1","userAgent":"node-fetch","responseSize":"383"}
{"timestamp":"2025-08-03T10:05:00.399Z","level":"info","message":"Performance: Slow API Response: POST /api/v1/translation completed in 3308ms","correlationId":"3d66e06a-a9b4-41fd-a853-0fbbd0a2227a","environment":"development","operation":"Slow API Response: POST /api/v1/translation","duration":3308,"type":"performance","statusCode":201}
{"timestamp":"2025-08-03T10:08:23.695Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"e8c3c011-7c42-4e4e-836b-030cded96f7c","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:08:27.546Z","level":"info","message":"API Call: GET /api/v1/health - 503 (3852ms)","correlationId":"e8c3c011-7c42-4e4e-836b-030cded96f7c","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":3852,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"1144"}
{"timestamp":"2025-08-03T10:08:27.547Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 3852ms","correlationId":"e8c3c011-7c42-4e4e-836b-030cded96f7c","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":3852,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T10:08:35.582Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"317e9620-eba9-40d5-997d-1bdec26437c5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:35.590Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (8ms)","correlationId":"317e9620-eba9-40d5-997d-1bdec26437c5","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":8,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T10:08:35.592Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"c35afe67-5546-40c2-bb3e-043a26d3bab1","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:35.648Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"7ce46775-a463-42d7-8133-e89a9eb98be1","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:35.655Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (7ms)","correlationId":"7ce46775-a463-42d7-8133-e89a9eb98be1","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":7,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T10:08:39.691Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4099ms)","correlationId":"c35afe67-5546-40c2-bb3e-043a26d3bab1","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4099,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:39.692Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4099ms","correlationId":"c35afe67-5546-40c2-bb3e-043a26d3bab1","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4099,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T10:08:39.694Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"2db4503a-2284-4243-8f72-e5d32c37fbc8","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:43.987Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4293ms)","correlationId":"2db4503a-2284-4243-8f72-e5d32c37fbc8","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4293,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:43.988Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4293ms","correlationId":"2db4503a-2284-4243-8f72-e5d32c37fbc8","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4293,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T10:08:52.796Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"a8efa45b-cd3d-42fc-b31b-d815ca3b8de7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:52.799Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (3ms)","correlationId":"a8efa45b-cd3d-42fc-b31b-d815ca3b8de7","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T10:08:52.801Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"5bd8d809-70a4-4832-9a6a-16daf76f49fe","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:52.835Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 0 (0ms)","correlationId":"9eded449-ed70-484f-bf1b-5b9671ff8d31","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:52.841Z","level":"info","message":"API Call: GET /api/v1/admin/dashboard - 401 (6ms)","correlationId":"9eded449-ed70-484f-bf1b-5b9671ff8d31","environment":"development","method":"GET","url":"/api/v1/admin/dashboard","statusCode":401,"duration":6,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"130"}
{"timestamp":"2025-08-03T10:08:56.841Z","level":"info","message":"API Call: GET /api/v1/health - 503 (4040ms)","correlationId":"5bd8d809-70a4-4832-9a6a-16daf76f49fe","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":4040,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:56.842Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 4040ms","correlationId":"5bd8d809-70a4-4832-9a6a-16daf76f49fe","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":4040,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T10:08:56.843Z","level":"info","message":"API Call: GET /api/v1/health - 0 (0ms)","correlationId":"9911797e-b044-4734-8ed7-b305a165541a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:59.589Z","level":"info","message":"API Call: GET /api/v1/health - 503 (2746ms)","correlationId":"9911797e-b044-4734-8ed7-b305a165541a","environment":"development","method":"GET","url":"/api/v1/health","statusCode":503,"duration":2746,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:08:59.591Z","level":"info","message":"Performance: Slow API Response: GET /api/v1/health completed in 2746ms","correlationId":"9911797e-b044-4734-8ed7-b305a165541a","environment":"development","operation":"Slow API Response: GET /api/v1/health","duration":2746,"type":"performance","statusCode":503}
{"timestamp":"2025-08-03T10:09:12.381Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"2e8b0868-6403-4137-854d-52bfba41df58","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:09:12.443Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (62ms)","correlationId":"2e8b0868-6403-4137-854d-52bfba41df58","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":62,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"156"}
{"timestamp":"2025-08-03T10:09:15.779Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"11d63d54-b123-4e0c-b149-8993673e32aa","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:09:15.786Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (7ms)","correlationId":"11d63d54-b123-4e0c-b149-8993673e32aa","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":7,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"156"}
{"timestamp":"2025-08-03T10:10:47.920Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"fb26a57d-a86d-4b93-859e-65c7907b6118","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:10:47.946Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (26ms)","correlationId":"fb26a57d-a86d-4b93-859e-65c7907b6118","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":26,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"156"}
{"timestamp":"2025-08-03T10:10:56.312Z","level":"info","message":"API Call: GET /api/v1/auth/login - 0 (0ms)","correlationId":"84cfaaec-dd96-4c4c-99e6-85ca8d38e161","environment":"development","method":"GET","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:10:56.313Z","level":"info","message":"API Call: GET /api/v1/auth/login - 404 (1ms)","correlationId":"84cfaaec-dd96-4c4c-99e6-85ca8d38e161","environment":"development","method":"GET","url":"/api/v1/auth/login","statusCode":404,"duration":1,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"162"}
{"timestamp":"2025-08-03T10:12:02.082Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"b58489d4-a9f8-458b-8957-08198a9b8983","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:12:02.106Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (24ms)","correlationId":"b58489d4-a9f8-458b-8957-08198a9b8983","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":24,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"156"}
{"timestamp":"2025-08-03T10:12:45.330Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"9510eb79-eb07-4162-9618-bcf880f5611c","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:12:45.352Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (22ms)","correlationId":"9510eb79-eb07-4162-9618-bcf880f5611c","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":22,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"156"}
{"timestamp":"2025-08-03T10:13:13.312Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"9dbd9eee-4761-4ef7-9052-b0011060ff9a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:13:13.336Z","level":"info","message":"API Call: POST /api/v1/auth/login - 401 (24ms)","correlationId":"9dbd9eee-4761-4ef7-9052-b0011060ff9a","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":401,"duration":24,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"156"}
{"timestamp":"2025-08-03T10:15:44.444Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"5bf69fa1-d354-42a2-8a26-64c1c8d50053","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"curl/8.12.1"}
{"timestamp":"2025-08-03T10:15:44.573Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (129ms)","correlationId":"5bf69fa1-d354-42a2-8a26-64c1c8d50053","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":129,"type":"api","ip":"::1","userAgent":"curl/8.12.1","responseSize":"461"}
{"timestamp":"2025-08-03T10:16:00.938Z","level":"info","message":"API Call: POST /api/v1/auth/login - 0 (0ms)","correlationId":"b53724ce-1d9f-43a9-85ea-969a9eb13522","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:01.015Z","level":"info","message":"API Call: POST /api/v1/auth/login - 201 (76ms)","correlationId":"b53724ce-1d9f-43a9-85ea-969a9eb13522","environment":"development","method":"POST","url":"/api/v1/auth/login","statusCode":201,"duration":76,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"461"}
{"timestamp":"2025-08-03T10:16:15.459Z","level":"info","message":"API Call: GET /api/v1/translation/history?page=1&limit=20 - 0 (0ms)","correlationId":"2bff503f-4ed3-492c-92f2-d03fa14c0d41","environment":"development","method":"GET","url":"/api/v1/translation/history?page=1&limit=20","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:15.470Z","level":"info","message":"API Call: GET /api/v1/translation/history?page=1&limit=20 - 401 (11ms)","correlationId":"2bff503f-4ed3-492c-92f2-d03fa14c0d41","environment":"development","method":"GET","url":"/api/v1/translation/history?page=1&limit=20","statusCode":401,"duration":11,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"150"}
{"timestamp":"2025-08-03T10:16:15.473Z","level":"info","message":"API Call: GET /api/v1/translation/history?page=1&limit=20 - 0 (0ms)","correlationId":"7a7f57b4-2554-42f2-9953-3b203ba70050","environment":"development","method":"GET","url":"/api/v1/translation/history?page=1&limit=20","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:15.483Z","level":"info","message":"API Call: GET /api/v1/translation/history?page=1&limit=20 - 401 (10ms)","correlationId":"7a7f57b4-2554-42f2-9953-3b203ba70050","environment":"development","method":"GET","url":"/api/v1/translation/history?page=1&limit=20","statusCode":401,"duration":10,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"150"}
{"timestamp":"2025-08-03T10:16:16.316Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 0 (0ms)","correlationId":"c4204716-bdb8-44b2-bef0-be5d3ea59c31","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.319Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 404 (3ms)","correlationId":"c4204716-bdb8-44b2-bef0-be5d3ea59c31","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":404,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"184"}
{"timestamp":"2025-08-03T10:16:16.321Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 0 (0ms)","correlationId":"3047f6b5-823a-43e1-b505-a9b4bc689836","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.323Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 404 (2ms)","correlationId":"3047f6b5-823a-43e1-b505-a9b4bc689836","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":404,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"186"}
{"timestamp":"2025-08-03T10:16:16.326Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 0 (0ms)","correlationId":"cc61241f-1b54-4ed4-9325-3d2a2f81e813","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.329Z","level":"info","message":"API Call: GET /api/v1/ai/qa/popular?limit=6 - 404 (3ms)","correlationId":"cc61241f-1b54-4ed4-9325-3d2a2f81e813","environment":"development","method":"GET","url":"/api/v1/ai/qa/popular?limit=6","statusCode":404,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"184"}
{"timestamp":"2025-08-03T10:16:16.332Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 0 (0ms)","correlationId":"907df8b6-3d99-47cd-9e91-cd528851dbcb","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.333Z","level":"info","message":"API Call: GET /api/v1/ai/qa/history?limit=50 - 404 (1ms)","correlationId":"907df8b6-3d99-47cd-9e91-cd528851dbcb","environment":"development","method":"GET","url":"/api/v1/ai/qa/history?limit=50","statusCode":404,"duration":1,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"186"}
{"timestamp":"2025-08-03T10:16:16.994Z","level":"info","message":"API Call: GET /api/v1/user/stats - 0 (0ms)","correlationId":"e3c48de5-a44f-4353-a154-a82b97805069","environment":"development","method":"GET","url":"/api/v1/user/stats","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.995Z","level":"info","message":"API Call: GET /api/v1/user/stats - 404 (2ms)","correlationId":"e3c48de5-a44f-4353-a154-a82b97805069","environment":"development","method":"GET","url":"/api/v1/user/stats","statusCode":404,"duration":2,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"162"}
{"timestamp":"2025-08-03T10:16:16.996Z","level":"info","message":"API Call: GET /api/v1/user/stats - 0 (0ms)","correlationId":"65758fbd-3782-4b50-8da8-12c6b06eb676","environment":"development","method":"GET","url":"/api/v1/user/stats","statusCode":0,"duration":0,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"timestamp":"2025-08-03T10:16:16.999Z","level":"info","message":"API Call: GET /api/v1/user/stats - 404 (3ms)","correlationId":"65758fbd-3782-4b50-8da8-12c6b06eb676","environment":"development","method":"GET","url":"/api/v1/user/stats","statusCode":404,"duration":3,"type":"api","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","responseSize":"162"}
